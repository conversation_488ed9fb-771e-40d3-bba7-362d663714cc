<?php
/**
 * View Receipt Page
 * MyGym Management System
 */

session_start();
require_once '../includes/Database.php';
require_once '../includes/Auth.php';
require_once '../includes/Config.php';

// Check authentication
if (!Auth::check()) {
    http_response_code(401);
    exit('Unauthorized');
}

// Get payment ID
$paymentId = $_GET['id'] ?? null;
if (!$paymentId || !is_numeric($paymentId)) {
    http_response_code(400);
    exit('Invalid payment ID');
}

try {
    $db = new Database();
    
    // Get payment details with member and plan information
    $payment = $db->fetch("
        SELECT p.*, 
               CONCAT(m.first_name, ' ', m.last_name) as member_name,
               m.member_id,
               m.phone,
               m.email,
               pl.name as plan_name,
               pl.duration_months,
               u.name as processed_by_name
        FROM payments p
        JOIN members m ON p.member_id = m.id
        LEFT JOIN plans pl ON p.plan_id = pl.id
        LEFT JOIN users u ON p.processed_by = u.id
        WHERE p.id = ?
    ", [$paymentId]);
    
    if (!$payment) {
        http_response_code(404);
        exit('Payment not found');
    }
    
    // Get gym settings
    $gymName = Config::get('gym_name', 'MyGym');
    $gymAddress = Config::get('gym_address', '');
    $gymPhone = Config::get('gym_phone', '');
    $gymEmail = Config::get('gym_email', '');
    $currency = Config::get('currency', 'USD');
    
    // Format currency
    function formatCurrency($amount, $currency = 'USD') {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'INR' => '₹',
            'JPY' => '¥'
        ];
        
        $symbol = $symbols[$currency] ?? $currency . ' ';
        return $symbol . number_format($amount, 2);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    exit('Error retrieving payment details');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt - <?= htmlspecialchars($payment['receipt_number']) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', sans-serif;
        }
        
        .receipt-container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-radius: 16px;
            overflow: hidden;
        }
        
        .receipt-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .receipt-body {
            padding: 2rem;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 500;
            color: #64748b;
        }
        
        .info-value {
            font-weight: 600;
            color: #1e293b;
        }
        
        .total-section {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border: 2px solid #e2e8f0;
        }
        
        .total-amount {
            font-size: 1.5rem;
            font-weight: 700;
            color: #059669;
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 500;
            background: #dcfce7;
            color: #166534;
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .btn {
            flex: 1;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            text-align: center;
            text-decoration: none;
            transition: all 0.2s;
            cursor: pointer;
            border: none;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
        }
        
        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            border: 1px solid #e2e8f0;
        }
        
        .btn-secondary:hover {
            background: #e2e8f0;
        }
        
        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e2e8f0;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen py-8">
    <div class="receipt-container">
        <!-- Header -->
        <div class="receipt-header">
            <div class="text-2xl font-bold mb-2"><?= htmlspecialchars($gymName) ?></div>
            <?php if ($gymAddress): ?>
                <div class="text-sm opacity-90 mb-1"><?= htmlspecialchars($gymAddress) ?></div>
            <?php endif; ?>
            <div class="text-sm opacity-90">
                <?php if ($gymPhone): ?>
                    <span>📞 <?= htmlspecialchars($gymPhone) ?></span>
                <?php endif; ?>
                <?php if ($gymEmail): ?>
                    <span class="ml-4">✉️ <?= htmlspecialchars($gymEmail) ?></span>
                <?php endif; ?>
            </div>
            
            <div class="mt-6">
                <div class="text-lg font-semibold">Payment Receipt</div>
                <div class="text-xl font-bold mt-2"><?= htmlspecialchars($payment['receipt_number']) ?></div>
            </div>
        </div>
        
        <!-- Body -->
        <div class="receipt-body">
            <!-- Member Information -->
            <div class="mb-6">
                <div class="section-title">
                    <i class="fas fa-user mr-2"></i>Member Information
                </div>
                <div class="info-row">
                    <span class="info-label">Member Name</span>
                    <span class="info-value"><?= htmlspecialchars($payment['member_name']) ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Member ID</span>
                    <span class="info-value"><?= htmlspecialchars($payment['member_id']) ?></span>
                </div>
                <?php if ($payment['phone']): ?>
                <div class="info-row">
                    <span class="info-label">Phone</span>
                    <span class="info-value"><?= htmlspecialchars($payment['phone']) ?></span>
                </div>
                <?php endif; ?>
                <?php if ($payment['email']): ?>
                <div class="info-row">
                    <span class="info-label">Email</span>
                    <span class="info-value"><?= htmlspecialchars($payment['email']) ?></span>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Membership Details -->
            <div class="mb-6">
                <div class="section-title">
                    <i class="fas fa-dumbbell mr-2"></i>Membership Details
                </div>
                <div class="info-row">
                    <span class="info-label">Plan</span>
                    <span class="info-value"><?= htmlspecialchars($payment['plan_name']) ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Duration</span>
                    <span class="info-value"><?= $payment['duration_months'] ?> month(s)</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Start Date</span>
                    <span class="info-value"><?= date('F j, Y', strtotime($payment['start_date'])) ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">End Date</span>
                    <span class="info-value"><?= date('F j, Y', strtotime($payment['end_date'])) ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Status</span>
                    <span class="status-badge">
                        <i class="fas fa-check-circle mr-1"></i>Active
                    </span>
                </div>
            </div>
            
            <!-- Payment Information -->
            <div class="mb-6">
                <div class="section-title">
                    <i class="fas fa-credit-card mr-2"></i>Payment Information
                </div>
                <div class="info-row">
                    <span class="info-label">Payment Date</span>
                    <span class="info-value"><?= date('F j, Y', strtotime($payment['payment_date'])) ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Payment Method</span>
                    <span class="info-value"><?= ucfirst(str_replace('_', ' ', $payment['payment_method'])) ?></span>
                </div>
                <?php if ($payment['transaction_id']): ?>
                <div class="info-row">
                    <span class="info-label">Transaction ID</span>
                    <span class="info-value"><?= htmlspecialchars($payment['transaction_id']) ?></span>
                </div>
                <?php endif; ?>
                <div class="info-row">
                    <span class="info-label">Processed By</span>
                    <span class="info-value">
                        <?php if ($payment['processed_by_name']): ?>
                            <?= htmlspecialchars($payment['processed_by_name']) ?>
                        <?php elseif (strpos($payment['notes'], 'Local Admin') !== false): ?>
                            Local Admin
                        <?php else: ?>
                            System
                        <?php endif; ?>
                    </span>
                </div>
            </div>
            
            <!-- Total Amount -->
            <div class="total-section">
                <div class="flex justify-between items-center">
                    <span class="text-lg font-semibold text-gray-700">Total Amount Paid</span>
                    <span class="total-amount"><?= formatCurrency($payment['amount'], $currency) ?></span>
                </div>
            </div>
            
            <?php if ($payment['notes'] && !strpos($payment['notes'], 'Local Admin')): ?>
            <!-- Notes -->
            <div class="mb-6">
                <div class="section-title">
                    <i class="fas fa-sticky-note mr-2"></i>Notes
                </div>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                    <p class="text-yellow-800"><?= htmlspecialchars(str_replace(' (Local Admin)', '', $payment['notes'])) ?></p>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Action Buttons -->
            <div class="action-buttons">
                <button onclick="printReceipt()" class="btn btn-primary">
                    <i class="fas fa-print mr-2"></i>Print Receipt
                </button>
                <button onclick="window.close()" class="btn btn-secondary">
                    <i class="fas fa-times mr-2"></i>Close
                </button>
            </div>
            
            <!-- Footer -->
            <div class="text-center mt-6 pt-6 border-t border-gray-200">
                <p class="text-sm text-gray-500">Thank you for your membership!</p>
                <p class="text-xs text-gray-400 mt-2">
                    Generated on <?= date('F j, Y \a\t g:i A') ?>
                </p>
            </div>
        </div>
    </div>
    
    <script>
        function printReceipt() {
            // Open print-optimized version
            const printWindow = window.open(`print-receipt.php?id=<?= $paymentId ?>&auto_print=1`, '_blank', 'width=400,height=600');
        }
    </script>
</body>
</html>
