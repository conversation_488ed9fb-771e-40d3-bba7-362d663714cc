<?php
/**
 * Equipment Management Page
 * MyGym Management System
 */

session_start();
require_once '../config/database.php';
require_once '../includes/auth.php';

// Require authentication and permission
Auth::requireAuth();
Auth::requirePermission('equipment.view');

$db = Database::getInstance();
$action = $_GET['action'] ?? 'list';
$equipmentId = $_GET['id'] ?? null;
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add' || $action === 'edit') {
        $result = handleEquipmentSave();
        if ($result['success']) {
            Session::success($result['message']);
            header('Location: equipment.php');
            exit;
        } else {
            $error = $result['message'];
        }
    } elseif ($action === 'delete') {
        $result = handleEquipmentDelete();
        if ($result['success']) {
            Session::success($result['message']);
        } else {
            Session::error($result['message']);
        }
        header('Location: equipment.php');
        exit;
    }
}

function handleEquipmentSave() {
    global $db, $action, $equipmentId;
    
    // Validate input
    $name = trim($_POST['name'] ?? '');
    $type = trim($_POST['type'] ?? '');
    $brand = trim($_POST['brand'] ?? '');
    $model = trim($_POST['model'] ?? '');
    $serialNumber = trim($_POST['serial_number'] ?? '');
    $purchaseDate = $_POST['purchase_date'] ?? null;
    $purchasePrice = $_POST['purchase_price'] ?? null;
    $conditionStatus = $_POST['condition_status'] ?? 'good';
    $maintenanceDate = $_POST['maintenance_date'] ?? null;
    $warrantyExpiry = $_POST['warranty_expiry'] ?? null;
    $location = trim($_POST['location'] ?? '');
    $notes = trim($_POST['notes'] ?? '');
    
    if (empty($name)) {
        return ['success' => false, 'message' => 'Equipment name is required.'];
    }
    
    if ($purchasePrice && (!is_numeric($purchasePrice) || $purchasePrice < 0)) {
        return ['success' => false, 'message' => 'Please enter a valid purchase price.'];
    }
    
    try {
        $db->beginTransaction();
        
        $data = [
            'name' => $name,
            'type' => $type ?: null,
            'brand' => $brand ?: null,
            'model' => $model ?: null,
            'serial_number' => $serialNumber ?: null,
            'purchase_date' => $purchaseDate ?: null,
            'purchase_price' => $purchasePrice ?: null,
            'condition_status' => $conditionStatus,
            'maintenance_date' => $maintenanceDate ?: null,
            'warranty_expiry' => $warrantyExpiry ?: null,
            'location' => $location ?: null,
            'notes' => $notes ?: null
        ];
        
        if ($action === 'add') {
            $data['is_active'] = 1;
            $newEquipmentId = $db->insert('equipment', $data);
            
            // Log activity
            ActivityLogger::log('Equipment Created', 'equipment', $newEquipmentId, null, $data);
            
            $message = 'Equipment added successfully.';
        } else {
            // Update existing equipment
            $oldData = $db->fetch("SELECT * FROM equipment WHERE id = ?", [$equipmentId]);
            $db->update('equipment', $data, 'id = ?', [$equipmentId]);
            
            // Log activity
            ActivityLogger::log('Equipment Updated', 'equipment', $equipmentId, $oldData, $data);
            
            $message = 'Equipment updated successfully.';
        }
        
        $db->commit();
        return ['success' => true, 'message' => $message];
        
    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'message' => 'Failed to save equipment: ' . $e->getMessage()];
    }
}

function handleEquipmentDelete() {
    global $db;
    
    $equipmentId = $_POST['equipment_id'] ?? null;
    if (!$equipmentId) {
        return ['success' => false, 'message' => 'Equipment ID is required.'];
    }
    
    try {
        $equipment = $db->fetch("SELECT * FROM equipment WHERE id = ?", [$equipmentId]);
        if (!$equipment) {
            return ['success' => false, 'message' => 'Equipment not found.'];
        }
        
        $db->beginTransaction();
        
        // Soft delete equipment
        $db->update('equipment', ['is_active' => 0], 'id = ?', [$equipmentId]);
        
        // Log activity
        ActivityLogger::log('Equipment Deleted', 'equipment', $equipmentId, $equipment);
        
        $db->commit();
        return ['success' => true, 'message' => 'Equipment deleted successfully.'];
        
    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'message' => 'Failed to delete equipment: ' . $e->getMessage()];
    }
}

// Get equipment data for edit
$equipment = null;
if ($action === 'edit' && $equipmentId) {
    $equipment = $db->fetch("SELECT * FROM equipment WHERE id = ?", [$equipmentId]);
    if (!$equipment) {
        Session::error('Equipment not found.');
        header('Location: equipment.php');
        exit;
    }
}

// Get equipment list with pagination and search
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;
$search = trim($_GET['search'] ?? '');
$typeFilter = $_GET['type'] ?? '';
$conditionFilter = $_GET['condition'] ?? '';
$statusFilter = $_GET['status'] ?? '';

$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(name LIKE ? OR brand LIKE ? OR model LIKE ? OR serial_number LIKE ? OR location LIKE ?)";
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm]);
}

if ($typeFilter) {
    $whereConditions[] = "type = ?";
    $params[] = $typeFilter;
}

if ($conditionFilter) {
    $whereConditions[] = "condition_status = ?";
    $params[] = $conditionFilter;
}

if ($statusFilter !== '') {
    $whereConditions[] = "is_active = ?";
    $params[] = $statusFilter;
}

$whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get total count
$totalEquipment = $db->fetch("SELECT COUNT(*) as count FROM equipment $whereClause", $params)['count'];
$totalPages = ceil($totalEquipment / $limit);

// Get equipment
$equipmentList = $db->fetchAll("
    SELECT *
    FROM equipment
    $whereClause
    ORDER BY created_at DESC
    LIMIT $limit OFFSET $offset
", $params);

// Get equipment types for filter
$equipmentTypes = $db->fetchAll("SELECT DISTINCT type FROM equipment WHERE type IS NOT NULL AND type != '' ORDER BY type");

$pageTitle = 'Equipment';
include '../includes/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Equipment</h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Manage your gym equipment inventory</p>
        </div>
        
        <?php if (Auth::can('equipment.create')): ?>
        <div class="mt-4 sm:mt-0">
            <a href="equipment.php?action=add" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                <i class="fas fa-plus mr-2"></i>
                Add Equipment
            </a>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Equipment</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($totalEquipment) ?></p>
                </div>
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-dumbbell text-blue-600 dark:text-blue-400"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Equipment</p>
                    <?php
                    $activeEquipment = $db->fetch("SELECT COUNT(*) as count FROM equipment WHERE is_active = 1")['count'];
                    ?>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($activeEquipment) ?></p>
                </div>
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Needs Maintenance</p>
                    <?php
                    $needsMaintenance = $db->fetch("SELECT COUNT(*) as count FROM equipment WHERE condition_status IN ('fair', 'poor') AND is_active = 1")['count'];
                    ?>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($needsMaintenance) ?></p>
                </div>
                <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-wrench text-yellow-600 dark:text-yellow-400"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Out of Order</p>
                    <?php
                    $outOfOrder = $db->fetch("SELECT COUNT(*) as count FROM equipment WHERE condition_status = 'out_of_order' AND is_active = 1")['count'];
                    ?>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($outOfOrder) ?></p>
                </div>
                <div class="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                </div>
            </div>
        </div>
    </div>

    <?php if ($action === 'list'): ?>
    <!-- Filters and Search -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                <input type="text"
                       name="search"
                       value="<?= htmlspecialchars($search) ?>"
                       placeholder="Search equipment..."
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Type</label>
                <select name="type" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option value="">All Types</option>
                    <?php foreach ($equipmentTypes as $type): ?>
                        <option value="<?= htmlspecialchars($type['type']) ?>" <?= $typeFilter === $type['type'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($type['type']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Condition</label>
                <select name="condition" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option value="">All Conditions</option>
                    <option value="excellent" <?= $conditionFilter === 'excellent' ? 'selected' : '' ?>>Excellent</option>
                    <option value="good" <?= $conditionFilter === 'good' ? 'selected' : '' ?>>Good</option>
                    <option value="fair" <?= $conditionFilter === 'fair' ? 'selected' : '' ?>>Fair</option>
                    <option value="poor" <?= $conditionFilter === 'poor' ? 'selected' : '' ?>>Poor</option>
                    <option value="out_of_order" <?= $conditionFilter === 'out_of_order' ? 'selected' : '' ?>>Out of Order</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                <select name="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option value="">All Status</option>
                    <option value="1" <?= $statusFilter === '1' ? 'selected' : '' ?>>Active</option>
                    <option value="0" <?= $statusFilter === '0' ? 'selected' : '' ?>>Inactive</option>
                </select>
            </div>

            <div class="flex items-end space-x-2">
                <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-search mr-2"></i>Filter
                </button>
                <a href="equipment.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </form>
    </div>

    <!-- Equipment Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php foreach ($equipmentList as $equipmentItem): ?>
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700 <?= !$equipmentItem['is_active'] ? 'opacity-60' : '' ?>">
            <div class="flex items-start justify-between mb-4">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <?php if ($equipmentItem['image']): ?>
                            <img src="<?= htmlspecialchars($equipmentItem['image']) ?>" alt="" class="w-12 h-12 rounded-full object-cover">
                        <?php else: ?>
                            <i class="fas fa-dumbbell text-blue-600 dark:text-blue-400"></i>
                        <?php endif; ?>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                            <?= htmlspecialchars($equipmentItem['name']) ?>
                        </h3>
                        <?php if ($equipmentItem['type']): ?>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                <?= htmlspecialchars($equipmentItem['type']) ?>
                            </p>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="flex flex-col items-end space-y-1">
                    <?php
                    $conditionColors = [
                        'excellent' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
                        'good' => 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
                        'fair' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
                        'poor' => 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
                        'out_of_order' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    ];
                    $conditionColor = $conditionColors[$equipmentItem['condition_status']] ?? $conditionColors['good'];
                    ?>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?= $conditionColor ?>">
                        <?= ucfirst(str_replace('_', ' ', $equipmentItem['condition_status'])) ?>
                    </span>

                    <?php if (!$equipmentItem['is_active']): ?>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                            Inactive
                        </span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="space-y-2 mb-4">
                <?php if ($equipmentItem['brand'] || $equipmentItem['model']): ?>
                    <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-tag mr-2"></i>
                        <?= htmlspecialchars(trim($equipmentItem['brand'] . ' ' . $equipmentItem['model'])) ?>
                    </div>
                <?php endif; ?>

                <?php if ($equipmentItem['serial_number']): ?>
                    <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-barcode mr-2"></i>
                        SN: <?= htmlspecialchars($equipmentItem['serial_number']) ?>
                    </div>
                <?php endif; ?>

                <?php if ($equipmentItem['location']): ?>
                    <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-map-marker-alt mr-2"></i>
                        <?= htmlspecialchars($equipmentItem['location']) ?>
                    </div>
                <?php endif; ?>

                <?php if ($equipmentItem['purchase_date']): ?>
                    <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-calendar mr-2"></i>
                        Purchased: <?= formatDate($equipmentItem['purchase_date']) ?>
                    </div>
                <?php endif; ?>

                <?php if ($equipmentItem['purchase_price']): ?>
                    <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-dollar-sign mr-2"></i>
                        <?= formatCurrency($equipmentItem['purchase_price']) ?>
                    </div>
                <?php endif; ?>
            </div>

            <?php if ($equipmentItem['notes']): ?>
                <div class="mb-4">
                    <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                        <?= htmlspecialchars($equipmentItem['notes']) ?>
                    </p>
                </div>
            <?php endif; ?>

            <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                <div class="flex items-center space-x-2">
                    <?php if (Auth::can('equipment.edit')): ?>
                        <a href="equipment.php?action=edit&id=<?= $equipmentItem['id'] ?>"
                           class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                            <i class="fas fa-edit"></i>
                        </a>
                    <?php endif; ?>

                    <?php if (Auth::can('equipment.delete')): ?>
                        <form method="POST" class="inline" onsubmit="return confirmDelete('Are you sure you want to delete this equipment?')">
                            <input type="hidden" name="equipment_id" value="<?= $equipmentItem['id'] ?>">
                            <input type="hidden" name="action" value="delete">
                            <?= Auth::csrfField() ?>
                            <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    <?php endif; ?>
                </div>

                <span class="text-xs text-gray-500 dark:text-gray-400">
                    Added <?= timeAgo($equipmentItem['created_at']) ?>
                </span>
            </div>
        </div>
        <?php endforeach; ?>

        <?php if (empty($equipmentList)): ?>
        <div class="col-span-full">
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-12 text-center shadow-sm border border-gray-100 dark:border-gray-700">
                <i class="fas fa-dumbbell text-4xl text-gray-400 mb-4"></i>
                <p class="text-lg font-medium text-gray-500 dark:text-gray-400">No equipment found</p>
                <p class="mt-1 text-gray-400 dark:text-gray-500">Get started by adding your first equipment.</p>
                <?php if (Auth::can('equipment.create')): ?>
                    <a href="equipment.php?action=add"
                       class="inline-flex items-center mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Add First Equipment
                    </a>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php if ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit Equipment Form -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                <?= $action === 'add' ? 'Add New Equipment' : 'Edit Equipment' ?>
            </h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                <?= $action === 'add' ? 'Add equipment to your gym inventory.' : 'Update the equipment information.' ?>
            </p>
        </div>

        <?php if ($error): ?>
            <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex">
                    <i class="fas fa-exclamation-circle text-red-400 mt-0.5 mr-3"></i>
                    <span class="text-red-800"><?= htmlspecialchars($error) ?></span>
                </div>
            </div>
        <?php endif; ?>

        <form method="POST" enctype="multipart/form-data" class="space-y-6">
            <?= Auth::csrfField() ?>

            <!-- Basic Information -->
            <div>
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Basic Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Equipment Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               id="name"
                               name="name"
                               value="<?= htmlspecialchars($equipment['name'] ?? $_POST['name'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               required>
                    </div>

                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Type</label>
                        <input type="text"
                               id="type"
                               name="type"
                               value="<?= htmlspecialchars($equipment['type'] ?? $_POST['type'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               placeholder="e.g., Cardio, Strength, Free Weights">
                    </div>

                    <div>
                        <label for="brand" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Brand</label>
                        <input type="text"
                               id="brand"
                               name="brand"
                               value="<?= htmlspecialchars($equipment['brand'] ?? $_POST['brand'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>

                    <div>
                        <label for="model" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Model</label>
                        <input type="text"
                               id="model"
                               name="model"
                               value="<?= htmlspecialchars($equipment['model'] ?? $_POST['model'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>

                    <div>
                        <label for="serial_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Serial Number</label>
                        <input type="text"
                               id="serial_number"
                               name="serial_number"
                               value="<?= htmlspecialchars($equipment['serial_number'] ?? $_POST['serial_number'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>

                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Location</label>
                        <input type="text"
                               id="location"
                               name="location"
                               value="<?= htmlspecialchars($equipment['location'] ?? $_POST['location'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               placeholder="e.g., Main Floor, Cardio Area">
                    </div>
                </div>
            </div>

            <!-- Purchase Information -->
            <div>
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Purchase Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="purchase_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Purchase Date</label>
                        <input type="date"
                               id="purchase_date"
                               name="purchase_date"
                               value="<?= htmlspecialchars($equipment['purchase_date'] ?? $_POST['purchase_date'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>

                    <div>
                        <label for="purchase_price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Purchase Price</label>
                        <input type="number"
                               id="purchase_price"
                               name="purchase_price"
                               value="<?= htmlspecialchars($equipment['purchase_price'] ?? $_POST['purchase_price'] ?? '') ?>"
                               step="0.01"
                               min="0"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               placeholder="0.00">
                    </div>

                    <div>
                        <label for="warranty_expiry" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Warranty Expiry</label>
                        <input type="date"
                               id="warranty_expiry"
                               name="warranty_expiry"
                               value="<?= htmlspecialchars($equipment['warranty_expiry'] ?? $_POST['warranty_expiry'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>
                </div>
            </div>

            <!-- Condition & Maintenance -->
            <div>
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Condition & Maintenance</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="condition_status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Condition</label>
                        <select id="condition_status"
                                name="condition_status"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <option value="excellent" <?= ($equipment['condition_status'] ?? $_POST['condition_status'] ?? 'good') === 'excellent' ? 'selected' : '' ?>>Excellent</option>
                            <option value="good" <?= ($equipment['condition_status'] ?? $_POST['condition_status'] ?? 'good') === 'good' ? 'selected' : '' ?>>Good</option>
                            <option value="fair" <?= ($equipment['condition_status'] ?? $_POST['condition_status'] ?? 'good') === 'fair' ? 'selected' : '' ?>>Fair</option>
                            <option value="poor" <?= ($equipment['condition_status'] ?? $_POST['condition_status'] ?? 'good') === 'poor' ? 'selected' : '' ?>>Poor</option>
                            <option value="out_of_order" <?= ($equipment['condition_status'] ?? $_POST['condition_status'] ?? 'good') === 'out_of_order' ? 'selected' : '' ?>>Out of Order</option>
                        </select>
                    </div>

                    <div>
                        <label for="maintenance_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Last Maintenance</label>
                        <input type="date"
                               id="maintenance_date"
                               name="maintenance_date"
                               value="<?= htmlspecialchars($equipment['maintenance_date'] ?? $_POST['maintenance_date'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>
                </div>
            </div>

            <!-- Notes -->
            <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes</label>
                <textarea id="notes"
                          name="notes"
                          rows="4"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                          placeholder="Any additional notes about the equipment..."><?= htmlspecialchars($equipment['notes'] ?? $_POST['notes'] ?? '') ?></textarea>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <a href="equipment.php"
                   class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition duration-200">
                    Cancel
                </a>
                <button type="submit"
                        class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                    <i class="fas fa-save mr-2"></i>
                    <?= $action === 'add' ? 'Add Equipment' : 'Update Equipment' ?>
                </button>
            </div>
        </form>
    </div>
    <?php endif; ?>
</div>

<?php include '../includes/footer.php'; ?>
