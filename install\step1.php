<!-- Step 1: Database Setup -->
<div class="text-center mb-6">
    <i class="fas fa-database text-4xl text-blue-600 mb-4"></i>
    <h2 class="text-2xl font-bold text-gray-900 mb-2">Database Configuration</h2>
    <p class="text-gray-600">Configure your MySQL database connection</p>
</div>

<form method="POST" class="space-y-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <label for="db_host" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-server mr-2"></i>Database Host
            </label>
            <input type="text" 
                   id="db_host" 
                   name="db_host" 
                   value="<?= htmlspecialchars($_POST['db_host'] ?? 'localhost') ?>"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                   placeholder="localhost"
                   required>
            <p class="mt-1 text-sm text-gray-500">Usually 'localhost' for local installations</p>
        </div>
        
        <div>
            <label for="db_name" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-database mr-2"></i>Database Name
            </label>
            <input type="text" 
                   id="db_name" 
                   name="db_name" 
                   value="<?= htmlspecialchars($_POST['db_name'] ?? 'mygym') ?>"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                   placeholder="mygym"
                   required>
            <p class="mt-1 text-sm text-gray-500">Database will be created if it doesn't exist</p>
        </div>
        
        <div>
            <label for="db_username" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-user mr-2"></i>Database Username
            </label>
            <input type="text" 
                   id="db_username" 
                   name="db_username" 
                   value="<?= htmlspecialchars($_POST['db_username'] ?? 'root') ?>"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                   placeholder="root"
                   required>
        </div>
        
        <div>
            <label for="db_password" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-lock mr-2"></i>Database Password
            </label>
            <input type="password" 
                   id="db_password" 
                   name="db_password" 
                   value="<?= htmlspecialchars($_POST['db_password'] ?? '') ?>"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                   placeholder="Enter database password">
            <p class="mt-1 text-sm text-gray-500">Leave empty if no password is set</p>
        </div>
    </div>
    
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex">
            <i class="fas fa-info-circle text-blue-400 mt-1 mr-3"></i>
            <div class="text-blue-800">
                <h4 class="font-medium">Database Requirements:</h4>
                <ul class="mt-2 text-sm list-disc list-inside">
                    <li>MySQL 5.7+ or MariaDB 10.2+</li>
                    <li>User must have CREATE, ALTER, INSERT, UPDATE, DELETE privileges</li>
                    <li>UTF8MB4 character set support</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="flex justify-end">
        <button type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-lg transition duration-200 flex items-center">
            Test Connection & Continue
            <i class="fas fa-arrow-right ml-2"></i>
        </button>
    </div>
</form>
