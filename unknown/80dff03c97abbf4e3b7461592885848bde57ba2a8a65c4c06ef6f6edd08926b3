<!-- Step 3: Admin Account Setup -->
<div class="text-center mb-6">
    <i class="fas fa-user-shield text-4xl text-blue-600 mb-4"></i>
    <h2 class="text-2xl font-bold text-gray-900 mb-2">Admin Account</h2>
    <p class="text-gray-600">Create your administrator account</p>
</div>

<form method="POST" class="space-y-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="md:col-span-2">
            <label for="admin_name" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-user mr-2"></i>Full Name
            </label>
            <input type="text" 
                   id="admin_name" 
                   name="admin_name" 
                   value="<?= htmlspecialchars($_POST['admin_name'] ?? '') ?>"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                   placeholder="John Doe"
                   required>
        </div>
        
        <div class="md:col-span-2">
            <label for="admin_email" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-envelope mr-2"></i>Email Address
            </label>
            <input type="email" 
                   id="admin_email" 
                   name="admin_email" 
                   value="<?= htmlspecialchars($_POST['admin_email'] ?? '') ?>"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                   placeholder="<EMAIL>"
                   required>
            <p class="mt-1 text-sm text-gray-500">This will be your login email</p>
        </div>
        
        <div>
            <label for="admin_password" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-lock mr-2"></i>Password
            </label>
            <input type="password" 
                   id="admin_password" 
                   name="admin_password" 
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                   placeholder="Enter a strong password"
                   minlength="6"
                   required>
            <p class="mt-1 text-sm text-gray-500">Minimum 6 characters</p>
        </div>
        
        <div>
            <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-lock mr-2"></i>Confirm Password
            </label>
            <input type="password" 
                   id="confirm_password" 
                   name="confirm_password" 
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                   placeholder="Confirm your password"
                   minlength="6"
                   required>
        </div>
    </div>
    
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex">
            <i class="fas fa-exclamation-triangle text-yellow-400 mt-1 mr-3"></i>
            <div class="text-yellow-800">
                <h4 class="font-medium">Security Notice:</h4>
                <ul class="mt-2 text-sm list-disc list-inside">
                    <li>Use a strong password with letters, numbers, and symbols</li>
                    <li>This account will have full administrative access</li>
                    <li>You can create additional staff accounts later</li>
                    <li>Keep your login credentials secure</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="flex justify-between">
        <a href="install.php?step=2" 
           class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-8 rounded-lg transition duration-200 flex items-center">
            <i class="fas fa-arrow-left mr-2"></i>
            Back
        </a>
        
        <button type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-lg transition duration-200 flex items-center">
            Create Account
            <i class="fas fa-arrow-right ml-2"></i>
        </button>
    </div>
</form>

<script>
// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('admin_password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});

document.getElementById('admin_password').addEventListener('input', function() {
    const confirmPassword = document.getElementById('confirm_password');
    if (confirmPassword.value) {
        confirmPassword.dispatchEvent(new Event('input'));
    }
});
</script>
