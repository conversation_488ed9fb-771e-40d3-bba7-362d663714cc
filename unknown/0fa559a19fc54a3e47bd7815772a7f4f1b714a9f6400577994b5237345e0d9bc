<?php
/**
 * Force Installation with Detailed Error Reporting
 * MyGym Management System
 */

session_start();

echo "<h2>🔧 Force Installation with Full Debug</h2>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .info { color: blue; } .warning { color: orange; } .step { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0; }</style>";

// Configuration
$config = [
    'db_host' => 'localhost',
    'db_name' => 'mygym',
    'db_username' => 'root',
    'db_password' => '',
    'admin_name' => 'Admin User',
    'admin_email' => '<EMAIL>',
    'admin_password' => 'admin123456'
];

echo "<div class='step'>";
echo "<h3>📋 Configuration:</h3>";
foreach ($config as $key => $value) {
    $display = ($key === 'admin_password') ? str_repeat('*', strlen($value)) : $value;
    echo "<strong>$key:</strong> $display<br>";
}
echo "</div>";

try {
    // Step 1: Test MySQL Connection
    echo "<div class='step'>";
    echo "<h3>🔌 Step 1: MySQL Connection Test</h3>";
    
    $dsn = "mysql:host={$config['db_host']};charset=utf8mb4";
    echo "<p class='info'>Connecting to: $dsn</p>";
    echo "<p class='info'>Username: {$config['db_username']}</p>";
    echo "<p class='info'>Password: " . (empty($config['db_password']) ? '(empty)' : '(set)') . "</p>";
    
    $pdo = new PDO($dsn, $config['db_username'], $config['db_password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ MySQL connection successful</p>";
    echo "</div>";
    
    // Step 2: Create Database
    echo "<div class='step'>";
    echo "<h3>🗄️ Step 2: Database Creation</h3>";
    
    $stmt = $pdo->prepare("CREATE DATABASE IF NOT EXISTS `{$config['db_name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $stmt->execute();
    echo "<p class='success'>✅ Database '{$config['db_name']}' created/verified</p>";
    
    // Connect to the specific database
    $dsn = "mysql:host={$config['db_host']};dbname={$config['db_name']};charset=utf8mb4";
    $pdo = new PDO($dsn, $config['db_username'], $config['db_password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Connected to database '{$config['db_name']}'</p>";
    echo "</div>";
    
    // Step 3: Check Schema File
    echo "<div class='step'>";
    echo "<h3>📄 Step 3: Schema File Check</h3>";
    
    if (!file_exists('schema.sql')) {
        throw new Exception("Schema file 'schema.sql' not found!");
    }
    
    $schema = file_get_contents('schema.sql');
    echo "<p class='success'>✅ Schema file loaded (" . strlen($schema) . " characters)</p>";
    
    // Show first few lines of schema
    $lines = explode("\n", $schema);
    echo "<p class='info'>First 5 lines of schema:</p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
    for ($i = 0; $i < min(5, count($lines)); $i++) {
        echo htmlspecialchars($lines[$i]) . "\n";
    }
    echo "</pre>";
    echo "</div>";
    
    // Step 4: Execute Schema
    echo "<div class='step'>";
    echo "<h3>🔨 Step 4: Schema Execution</h3>";
    
    // Split schema into individual statements
    $statements = array_filter(array_map('trim', explode(';', $schema)));
    echo "<p class='info'>Found " . count($statements) . " SQL statements</p>";
    
    $executedCount = 0;
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            try {
                $pdo->exec($statement);
                $executedCount++;
            } catch (PDOException $e) {
                echo "<p class='warning'>⚠️ Statement failed: " . substr($statement, 0, 50) . "...</p>";
                echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
            }
        }
    }
    echo "<p class='success'>✅ Executed $executedCount SQL statements</p>";
    
    // Verify tables
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "<p class='success'>✅ Tables created: " . implode(', ', $tables) . "</p>";
    
    if (!in_array('users', $tables)) {
        throw new Exception("Users table was not created!");
    }
    echo "</div>";
    
    // Step 5: Create Admin User
    echo "<div class='step'>";
    echo "<h3>👤 Step 5: Admin User Creation</h3>";
    
    // First, check current users
    $existingUsers = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    echo "<p class='info'>Current users in database: $existingUsers</p>";
    
    // Hash password
    $hashedPassword = password_hash($config['admin_password'], PASSWORD_DEFAULT);
    echo "<p class='info'>🔐 Password hashed: " . substr($hashedPassword, 0, 30) . "...</p>";
    echo "<p class='info'>🔐 Hash length: " . strlen($hashedPassword) . " characters</p>";
    
    // Check if user already exists
    $existingUser = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $existingUser->execute([$config['admin_email']]);
    if ($existingUser->fetch()) {
        echo "<p class='warning'>⚠️ User with email {$config['admin_email']} already exists, deleting...</p>";
        $pdo->prepare("DELETE FROM users WHERE email = ?")->execute([$config['admin_email']]);
    }
    
    // Insert admin user
    $sql = "INSERT INTO users (name, email, password, role, is_active) VALUES (?, ?, ?, 'admin', 1)";
    echo "<p class='info'>SQL: $sql</p>";
    echo "<p class='info'>Parameters: ['{$config['admin_name']}', '{$config['admin_email']}', '[HASH]', 'admin', 1]</p>";
    
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([$config['admin_name'], $config['admin_email'], $hashedPassword]);
    
    if ($result) {
        $userId = $pdo->lastInsertId();
        echo "<p class='success'>✅ Admin user created with ID: $userId</p>";
        
        // Verify insertion
        $user = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $user->execute([$userId]);
        $userData = $user->fetch(PDO::FETCH_ASSOC);
        
        if ($userData) {
            echo "<p class='success'>✅ User verification successful</p>";
            echo "<p class='info'>👤 ID: {$userData['id']}</p>";
            echo "<p class='info'>👤 Name: " . htmlspecialchars($userData['name']) . "</p>";
            echo "<p class='info'>📧 Email: " . htmlspecialchars($userData['email']) . "</p>";
            echo "<p class='info'>🔑 Role: " . htmlspecialchars($userData['role']) . "</p>";
            echo "<p class='info'>🟢 Active: " . ($userData['is_active'] ? 'Yes' : 'No') . "</p>";
            echo "<p class='info'>🔐 Password hash: " . substr($userData['password'], 0, 30) . "...</p>";
            
            // Test password verification
            if (password_verify($config['admin_password'], $userData['password'])) {
                echo "<p class='success'>✅ Password verification test PASSED</p>";
            } else {
                echo "<p class='error'>❌ Password verification test FAILED</p>";
            }
        } else {
            throw new Exception("User was inserted but cannot be retrieved!");
        }
    } else {
        throw new Exception("Failed to insert admin user");
    }
    echo "</div>";
    
    // Step 6: Create Config File
    echo "<div class='step'>";
    echo "<h3>⚙️ Step 6: Configuration File</h3>";
    
    $configContent = "<?php\nreturn [\n";
    $configContent .= "    'database' => [\n";
    $configContent .= "        'host' => '{$config['db_host']}',\n";
    $configContent .= "        'name' => '{$config['db_name']}',\n";
    $configContent .= "        'username' => '{$config['db_username']}',\n";
    $configContent .= "        'password' => '{$config['db_password']}'\n";
    $configContent .= "    ],\n";
    $configContent .= "    'app' => [\n";
    $configContent .= "        'name' => 'MyGym Fitness Center',\n";
    $configContent .= "        'url' => 'http://localhost/mygym',\n";
    $configContent .= "        'currency' => 'USD',\n";
    $configContent .= "        'language' => 'en'\n";
    $configContent .= "    ]\n";
    $configContent .= "];\n";
    
    if (!is_dir('config')) {
        mkdir('config', 0755, true);
    }
    
    file_put_contents('config/config.php', $configContent);
    echo "<p class='success'>✅ Configuration file created</p>";
    echo "</div>";
    
    // Final verification
    echo "<div class='step'>";
    echo "<h3>🔍 Final Verification</h3>";
    
    $totalUsers = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    echo "<p class='info'>👥 Total users in database: $totalUsers</p>";
    
    $users = $pdo->query("SELECT id, name, email, role, is_active FROM users")->fetchAll(PDO::FETCH_ASSOC);
    foreach ($users as $user) {
        echo "<p class='success'>👤 User {$user['id']}: " . htmlspecialchars($user['name']) . " (" . htmlspecialchars($user['email']) . ") - Role: {$user['role']}, Active: " . ($user['is_active'] ? 'Yes' : 'No') . "</p>";
    }
    echo "</div>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 20px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>🎉 Force Installation Complete!</h3>";
    echo "<p style='color: #155724;'>Admin user has been forcefully created in the database.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='step'>";
    echo "<h3 style='color: red;'>❌ Installation Failed</h3>";
    echo "<p class='error'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p class='info'>File: " . $e->getFile() . "</p>";
    echo "<p class='info'>Line: " . $e->getLine() . "</p>";
    echo "<p class='info'>Stack trace:</p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}

echo "<h3>🔑 Login Credentials:</h3>";
echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0;'>";
echo "<p><strong>Email:</strong> <EMAIL></p>";
echo "<p><strong>Password:</strong> admin123456</p>";
echo "</div>";

echo "<h3>🚀 Test Now:</h3>";
echo "<a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔑 Try Login</a>";
echo "<a href='test_credentials.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🧪 Test Credentials</a>";
?>
