<?php
/**
 * Debug Installation Script
 * MyGym Management System
 */

session_start();

echo "<h2>🔧 Debug Installation Process</h2>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .info { color: blue; } .warning { color: orange; } .step { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0; }</style>";

// Check if already installed
if (file_exists('config/config.php')) {
    echo '<p class="error">❌ System already installed. Please reset first.</p>';
    echo '<a href="reset_system.php" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🔄 Reset System</a>';
    exit;
}

// Installation configuration
$config = [
    'db_host' => 'localhost',
    'db_name' => 'mygym',
    'db_username' => 'root',
    'db_password' => '',
    'gym_name' => 'MyGym Fitness Center',
    'site_url' => 'http://localhost/mygym',
    'currency' => 'USD',
    'language' => 'en',
    'admin_name' => 'Admin User',
    'admin_email' => '<EMAIL>',
    'admin_password' => 'admin123456'
];

echo "<h3>📋 Installation Configuration:</h3>";
echo "<div class='step'>";
foreach ($config as $key => $value) {
    $displayValue = ($key === 'admin_password') ? str_repeat('*', strlen($value)) : $value;
    echo "<strong>" . ucfirst(str_replace('_', ' ', $key)) . ":</strong> " . htmlspecialchars($displayValue) . "<br>";
}
echo "</div>";

try {
    // Step 1: Database Connection Test
    echo "<div class='step'>";
    echo "<h4>🔌 Step 1: Database Connection Test</h4>";
    
    $dsn = "mysql:host={$config['db_host']};charset=utf8mb4";
    $pdo = new PDO($dsn, $config['db_username'], $config['db_password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ MySQL connection successful</p>";
    
    // Create database
    $stmt = $pdo->prepare("CREATE DATABASE IF NOT EXISTS `{$config['db_name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $stmt->execute();
    echo "<p class='success'>✅ Database '{$config['db_name']}' created/verified</p>";
    echo "</div>";
    
    // Step 2: Schema Installation
    echo "<div class='step'>";
    echo "<h4>🗄️ Step 2: Database Schema Installation</h4>";
    
    $dsn = "mysql:host={$config['db_host']};dbname={$config['db_name']};charset=utf8mb4";
    $pdo = new PDO($dsn, $config['db_username'], $config['db_password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if schema file exists
    if (!file_exists('schema.sql')) {
        throw new Exception("Schema file 'schema.sql' not found!");
    }
    
    $schema = file_get_contents('schema.sql');
    echo "<p class='info'>📄 Schema file loaded (" . strlen($schema) . " characters)</p>";
    
    // Execute schema
    $pdo->exec($schema);
    echo "<p class='success'>✅ Database schema executed</p>";
    
    // Verify tables were created
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "<p class='info'>📊 Tables created: " . implode(', ', $tables) . "</p>";
    
    // Check if users table exists
    if (!in_array('users', $tables)) {
        throw new Exception("Users table was not created!");
    }
    echo "<p class='success'>✅ Users table confirmed</p>";
    echo "</div>";
    
    // Step 3: Admin User Creation
    echo "<div class='step'>";
    echo "<h4>👤 Step 3: Admin User Creation</h4>";
    
    $hashedPassword = password_hash($config['admin_password'], PASSWORD_DEFAULT);
    echo "<p class='info'>🔐 Password hashed: " . substr($hashedPassword, 0, 30) . "...</p>";
    
    // Insert admin user
    $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, is_active) VALUES (?, ?, ?, 'admin', 1)");
    $result = $stmt->execute([$config['admin_name'], $config['admin_email'], $hashedPassword]);
    
    if ($result) {
        $userId = $pdo->lastInsertId();
        echo "<p class='success'>✅ Admin user created with ID: $userId</p>";
        
        // Verify user was inserted
        $user = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $user->execute([$userId]);
        $userData = $user->fetch(PDO::FETCH_ASSOC);
        
        if ($userData) {
            echo "<p class='success'>✅ User verification successful</p>";
            echo "<p class='info'>👤 Name: " . htmlspecialchars($userData['name']) . "</p>";
            echo "<p class='info'>📧 Email: " . htmlspecialchars($userData['email']) . "</p>";
            echo "<p class='info'>🔑 Role: " . htmlspecialchars($userData['role']) . "</p>";
            echo "<p class='info'>🟢 Active: " . ($userData['is_active'] ? 'Yes' : 'No') . "</p>";
            
            // Test password verification
            if (password_verify($config['admin_password'], $userData['password'])) {
                echo "<p class='success'>✅ Password verification test passed</p>";
            } else {
                echo "<p class='error'>❌ Password verification test failed</p>";
            }
        } else {
            throw new Exception("User was inserted but cannot be retrieved!");
        }
    } else {
        throw new Exception("Failed to insert admin user");
    }
    echo "</div>";
    
    // Step 4: Configuration File Creation
    echo "<div class='step'>";
    echo "<h4>⚙️ Step 4: Configuration File Creation</h4>";
    
    $configContent = "<?php\nreturn [\n";
    $configContent .= "    'database' => [\n";
    $configContent .= "        'host' => '{$config['db_host']}',\n";
    $configContent .= "        'name' => '{$config['db_name']}',\n";
    $configContent .= "        'username' => '{$config['db_username']}',\n";
    $configContent .= "        'password' => '{$config['db_password']}'\n";
    $configContent .= "    ],\n";
    $configContent .= "    'app' => [\n";
    $configContent .= "        'name' => '{$config['gym_name']}',\n";
    $configContent .= "        'url' => '{$config['site_url']}',\n";
    $configContent .= "        'currency' => '{$config['currency']}',\n";
    $configContent .= "        'language' => '{$config['language']}'\n";
    $configContent .= "    ]\n";
    $configContent .= "];\n";
    
    if (!is_dir('config')) {
        mkdir('config', 0755, true);
    }
    
    file_put_contents('config/config.php', $configContent);
    echo "<p class='success'>✅ Configuration file created</p>";
    echo "</div>";
    
    // Step 5: Site Settings
    echo "<div class='step'>";
    echo "<h4>🏢 Step 5: Site Settings Configuration</h4>";
    
    $settings = [
        'gym_name' => $config['gym_name'],
        'site_url' => $config['site_url'],
        'currency' => $config['currency'],
        'language' => $config['language'],
        'timezone' => 'UTC',
        'date_format' => 'Y-m-d',
        'time_format' => 'H:i'
    ];
    
    foreach ($settings as $key => $value) {
        $stmt = $pdo->prepare("INSERT INTO config (setting_key, setting_value) VALUES (?, ?)");
        $stmt->execute([$key, $value]);
    }
    echo "<p class='success'>✅ Site settings configured</p>";
    echo "</div>";
    
    // Final Verification
    echo "<div class='step'>";
    echo "<h4>🔍 Final Verification</h4>";
    
    // Count users
    $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    echo "<p class='info'>👥 Total users in database: $userCount</p>";
    
    // List all users
    $users = $pdo->query("SELECT id, name, email, role, is_active FROM users")->fetchAll(PDO::FETCH_ASSOC);
    foreach ($users as $user) {
        echo "<p class='info'>👤 User: " . htmlspecialchars($user['name']) . " (" . htmlspecialchars($user['email']) . ") - Role: " . $user['role'] . "</p>";
    }
    echo "</div>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 20px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>🎉 Installation Complete!</h3>";
    echo "<p style='color: #155724;'>Your MyGym Management System has been successfully installed with detailed debugging.</p>";
    echo "</div>";
    
    echo "<h3>🔑 Login Credentials:</h3>";
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0;'>";
    echo "<p><strong>Email:</strong> " . htmlspecialchars($config['admin_email']) . "</p>";
    echo "<p><strong>Password:</strong> " . htmlspecialchars($config['admin_password']) . "</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='step'>";
    echo "<h4 style='color: red;'>❌ Installation Failed</h4>";
    echo "<p class='error'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p class='info'>File: " . $e->getFile() . "</p>";
    echo "<p class='info'>Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>🚀 Next Steps:</h3>";
echo "<a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔑 Try Login</a>";
echo "<a href='debug_login.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 Debug Login</a>";
echo "<a href='test_credentials.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🧪 Test Credentials</a>";
?>
