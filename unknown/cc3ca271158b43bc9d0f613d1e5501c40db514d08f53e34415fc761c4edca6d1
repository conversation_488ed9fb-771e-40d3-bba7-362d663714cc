<?php
/**
 * System Reset Script
 * MyGym Management System
 */

session_start();

echo "<h2>🔄 MyGym System Reset</h2>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .info { color: blue; } .warning { color: orange; }</style>";

// Clear installation session data
unset($_SESSION['install_db']);
unset($_SESSION['install_site']);
unset($_SESSION['install_admin']);
unset($_SESSION['install_admin_email']);

// Clear any user session data
unset($_SESSION['user_id']);
unset($_SESSION['user_name']);
unset($_SESSION['user_email']);
unset($_SESSION['user_role']);

echo "<p class='success'>✅ Session data cleared</p>";

// Remove config file if it exists
if (file_exists('config/config.php')) {
    if (unlink('config/config.php')) {
        echo "<p class='success'>✅ Configuration file removed successfully</p>";
    } else {
        echo "<p class='error'>❌ Failed to remove configuration file</p>";
        echo "<p class='warning'>You may need to manually delete config/config.php</p>";
    }
} else {
    echo "<p class='success'>✅ Configuration file already removed</p>";
}

// Optional: Clear database tables if they exist
$clearDatabase = isset($_GET['clear_db']) && $_GET['clear_db'] === 'yes';

if ($clearDatabase) {
    echo "<h3>🗑️ Database Cleanup</h3>";
    try {
        // Try to connect to database if config exists
        if (file_exists('config/database.php')) {
            require_once 'config/database.php';

            // Check if we can connect
            $config = [
                'db_host' => 'localhost',
                'db_name' => 'mygym',
                'db_username' => 'root',
                'db_password' => ''
            ];

            $dsn = "mysql:host={$config['db_host']};dbname={$config['db_name']};charset=utf8mb4";
            $pdo = new PDO($dsn, $config['db_username'], $config['db_password']);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Drop all tables
            $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
            foreach ($tables as $table) {
                $pdo->exec("DROP TABLE IF EXISTS `$table`");
            }

            echo "<p class='success'>✅ Database tables cleared (" . count($tables) . " tables dropped)</p>";
        }
    } catch (Exception $e) {
        echo "<p class='warning'>⚠️ Could not clear database: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

echo "<p class='success'>🎉 System has been reset successfully!</p>";
echo "<p class='info'>You can now run the installation wizard.</p>";

if (!$clearDatabase) {
    echo "<p class='info'><a href='?clear_db=yes' style='color: #dc3545;'>🗑️ Also clear database tables</a></p>";
}

echo "<hr>";
echo "<h3>🚀 Next Steps:</h3>";
echo "<a href='install.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔧 Run Installation Wizard</a>";
echo "<a href='index.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 Go to Homepage</a>";

echo "<hr>";
echo "<h3>📋 Installation Steps:</h3>";
echo "<ol>";
echo "<li><strong>Step 1:</strong> Database Configuration (MySQL connection)</li>";
echo "<li><strong>Step 2:</strong> Site Settings (Gym name, URL, currency)</li>";
echo "<li><strong>Step 3:</strong> Admin Account (Create your login credentials)</li>";
echo "<li><strong>Step 4:</strong> Database Installation (Create tables and data)</li>";
echo "</ol>";

echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 20px 0;'>";
echo "<h4>💡 Installation Tips:</h4>";
echo "<ul>";
echo "<li>Make sure XAMPP/MySQL is running</li>";
echo "<li>Use 'localhost' as database host</li>";
echo "<li>Default MySQL username is usually 'root'</li>";
echo "<li>Password is often empty for local development</li>";
echo "<li>Choose a strong password for your admin account</li>";
echo "</ul>";
echo "</div>";
?>
