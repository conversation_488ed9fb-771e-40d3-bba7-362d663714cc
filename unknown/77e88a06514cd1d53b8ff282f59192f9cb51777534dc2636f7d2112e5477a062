<?php
/**
 * Login Debug Script
 * MyGym Management System
 */

session_start();

echo "<h2>🔍 Login Debug Analysis</h2>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .info { color: blue; } .warning { color: orange; } table { border-collapse: collapse; width: 100%; } th, td { border: 1px solid #ddd; padding: 8px; text-align: left; } th { background-color: #f2f2f2; }</style>";

// Check if system is installed
if (!file_exists('config/config.php')) {
    echo '<p class="error">❌ System not installed. Please run installation first.</p>';
    echo '<a href="auto_install.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🚀 Auto Install</a>';
    echo ' <a href="install.php" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🧙‍♂️ Manual Install</a>';
    exit;
}

try {
    // Load database
    require_once 'config/database.php';
    $db = Database::getInstance();
    echo "<p class='success'>✅ Database connection successful</p>";
    
    // Check if users table exists
    $tables = $db->fetchAll("SHOW TABLES LIKE 'users'");
    if (empty($tables)) {
        echo "<p class='error'>❌ Users table doesn't exist! Installation incomplete.</p>";
        echo "<p><a href='auto_install.php'>🔄 Re-run Installation</a></p>";
        exit;
    }
    echo "<p class='success'>✅ Users table exists</p>";
    
    // Show all users in database
    echo "<h3>👥 All Users in Database:</h3>";
    $allUsers = $db->fetchAll("SELECT id, name, email, role, is_active, created_at FROM users");
    
    if (empty($allUsers)) {
        echo "<p class='error'>❌ No users found in database!</p>";
        echo "<p class='warning'>The installation didn't create the admin user properly.</p>";
        echo "<p><a href='auto_install.php'>🔄 Re-run Installation</a></p>";
    } else {
        echo "<table>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Role</th><th>Active</th><th>Created</th><th>Password Hash (first 20 chars)</th></tr>";
        foreach ($allUsers as $user) {
            $passwordHash = $db->fetch("SELECT password FROM users WHERE id = ?", [$user['id']])['password'];
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . htmlspecialchars($user['name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . htmlspecialchars($user['role']) . "</td>";
            echo "<td>" . ($user['is_active'] ? '✅ Yes' : '❌ No') . "</td>";
            echo "<td>" . $user['created_at'] . "</td>";
            echo "<td>" . substr($passwordHash, 0, 20) . "...</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Test login for each user
        echo "<h3>🔐 Login Test for Each User:</h3>";
        
        $testPasswords = ['admin123456', 'password', '123456', 'admin', 'test'];
        
        foreach ($allUsers as $user) {
            echo "<h4>Testing user: " . htmlspecialchars($user['email']) . "</h4>";
            
            $fullUser = $db->fetch("SELECT * FROM users WHERE id = ?", [$user['id']]);
            
            echo "<div style='margin-left: 20px;'>";
            echo "<p><strong>Stored password hash:</strong> " . substr($fullUser['password'], 0, 50) . "...</p>";
            echo "<p><strong>Hash type:</strong> " . (substr($fullUser['password'], 0, 4) === '$2y$' ? '✅ bcrypt' : '❌ Unknown') . "</p>";
            
            $foundPassword = false;
            foreach ($testPasswords as $testPassword) {
                if (password_verify($testPassword, $fullUser['password'])) {
                    echo "<p class='success'>✅ Password '<strong>$testPassword</strong>' works!</p>";
                    $foundPassword = true;
                    break;
                }
            }
            
            if (!$foundPassword) {
                echo "<p class='error'>❌ None of the test passwords work</p>";
                echo "<p class='info'>Tested passwords: " . implode(', ', $testPasswords) . "</p>";
            }
            echo "</div>";
        }
    }
    
    // Test the actual login logic from index.php
    echo "<h3>🧪 Testing Login Logic:</h3>";
    
    if (!empty($allUsers)) {
        $testUser = $allUsers[0]; // Test with first user
        $testEmail = $testUser['email'];
        
        echo "<p>Testing login logic with email: <strong>$testEmail</strong></p>";
        
        // Simulate the exact query from index.php
        $loginUser = $db->fetch(
            "SELECT id, name, email, password, role, is_active FROM users WHERE email = ? AND is_active = 1",
            [$testEmail]
        );
        
        if ($loginUser) {
            echo "<p class='success'>✅ User found by login query</p>";
            echo "<p><strong>Query result:</strong></p>";
            echo "<pre>" . print_r($loginUser, true) . "</pre>";
            
            // Test password verification
            foreach ($testPasswords as $testPassword) {
                if (password_verify($testPassword, $loginUser['password'])) {
                    echo "<p class='success'>✅ Login would succeed with password: <strong>$testPassword</strong></p>";
                    
                    // Show what the session would contain
                    echo "<p><strong>Session would be set to:</strong></p>";
                    echo "<ul>";
                    echo "<li>user_id: " . $loginUser['id'] . "</li>";
                    echo "<li>user_name: " . htmlspecialchars($loginUser['name']) . "</li>";
                    echo "<li>user_email: " . htmlspecialchars($loginUser['email']) . "</li>";
                    echo "<li>user_role: " . htmlspecialchars($loginUser['role']) . "</li>";
                    echo "</ul>";
                    break;
                }
            }
        } else {
            echo "<p class='error'>❌ User not found by login query (might be inactive)</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p class='info'>Full error details:</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<h3>🔧 Quick Actions:</h3>";
echo "<a href='index.php' style='background: #007cba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;'>🔑 Try Login</a>";
echo "<a href='auto_install.php' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;'>🔄 Re-install</a>";
echo "<a href='reset_system.php' style='background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;'>🗑️ Reset System</a>";
?>
