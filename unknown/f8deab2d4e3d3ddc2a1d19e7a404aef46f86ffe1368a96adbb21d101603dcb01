# MyGym Management System

A comprehensive, modern gym management system built with PHP, MySQL, and Tailwind CSS. Features a beautiful, responsive interface with dark mode support and complete gym operations management.

## 🚀 Features

### Core Functionality
- **Member Management** - Complete member profiles, plans, and tracking
- **Payment Processing** - Record payments, generate receipts, track revenue
- **Trainer Management** - Manage trainers, specialties, and assignments
- **Plan Management** - Create and manage membership plans
- **Equipment Tracking** - Inventory management with maintenance tracking
- **Notifications** - Email/SMS notifications for expiring memberships
- **Reports & Analytics** - Comprehensive reporting with charts and insights

### Technical Features
- **Modern UI/UX** - Built with Tailwind CSS and Alpine.js
- **Responsive Design** - Works perfectly on desktop, tablet, and mobile
- **Dark Mode Support** - Toggle between light and dark themes
- **Role-Based Access Control** - Admin, Manager, and Receptionist roles
- **Activity Logging** - Track all system activities
- **CSRF Protection** - Secure forms and data handling
- **Session Management** - Secure user sessions with remember me
- **Configuration Management** - Easy system configuration

## 📋 Requirements

- **PHP 7.4+** with PDO extension
- **MySQL 5.7+** or MariaDB 10.2+
- **Web Server** (Apache/Nginx)
- **Modern Browser** with JavaScript enabled

## 🛠️ Installation

### 1. Download and Extract
```bash
# Download the system files
# Extract to your web server directory (e.g., /var/www/html/mygym)
```

### 2. Set Permissions
```bash
chmod 755 -R /path/to/mygym
chmod 777 uploads/ logs/ (if these directories exist)
```

### 3. Run Installation Wizard
1. Open your browser and navigate to: `http://yourdomain.com/mygym/install.php`
2. Follow the 4-step installation wizard:
   - **Step 1**: Database Configuration
   - **Step 2**: Site Settings
   - **Step 3**: Admin Account Creation
   - **Step 4**: Installation Complete

### 4. Login
- Navigate to: `http://yourdomain.com/mygym/`
- Login with your admin credentials
- Start managing your gym!

## 🎯 Default Login

After installation, you can login with the admin account you created during setup.

**Default Roles:**
- **Admin**: Full system access
- **Manager**: Member and trainer management
- **Receptionist**: Basic member operations

## 📱 Usage

### Dashboard
- View key metrics and statistics
- Monitor expiring memberships
- Quick access to common actions
- Revenue charts and analytics

### Member Management
- Add/edit member profiles
- Assign membership plans
- Track membership status
- Emergency contact information
- Payment history

### Payment Processing
- Record new payments
- Generate receipts
- Track payment methods
- Automatic membership renewal
- Payment history and reports

### Trainer Management
- Add trainer profiles
- Set specialties and rates
- Assign members to trainers
- Track trainer performance

### Equipment Management
- Inventory tracking
- Maintenance schedules
- Condition monitoring
- Purchase history

### Notifications
- Email notifications for expiring memberships
- SMS alerts (with API integration)
- Bulk messaging capabilities
- Automated reminders

### Reports
- Revenue analytics
- Member statistics
- Plan performance
- Payment method analysis
- Monthly trends

## ⚙️ Configuration

### Email Settings
Configure SMTP settings in **Settings > Email Settings**:
- SMTP Host (e.g., smtp.gmail.com)
- SMTP Port (587 for TLS)
- Username and Password
- Encryption (TLS/SSL)

### SMS Settings
Configure SMS API in **Settings > SMS Settings**:
- API Key from your SMS provider
- Sender ID/Number

### General Settings
- Gym information
- Currency and localization
- Date/time formats
- System preferences

## 🔧 Customization

### Themes
The system uses Tailwind CSS with built-in dark mode support. You can customize:
- Colors and branding
- Layout and spacing
- Component styles

### Languages
Currently supports English with easy localization framework for additional languages.

### Features
The modular design allows easy addition of new features:
- Custom member fields
- Additional payment methods
- New report types
- Integration with external services

## 🔒 Security Features

- **Password Hashing** - Secure bcrypt password hashing
- **CSRF Protection** - All forms protected against CSRF attacks
- **SQL Injection Prevention** - Prepared statements throughout
- **Session Security** - Secure session handling
- **Role-Based Access** - Granular permission system
- **Activity Logging** - Complete audit trail

## 📊 Database Schema

The system includes these main tables:
- `users` - System users and authentication
- `members` - Gym member profiles
- `trainers` - Trainer information
- `plans` - Membership plans
- `payments` - Payment records
- `equipment` - Equipment inventory
- `activity_logs` - System activity tracking
- `settings` - Configuration storage

## 🤝 Support

### Documentation
- Built-in help system
- Comprehensive admin interface
- Activity logs for troubleshooting

### Common Issues
1. **Database Connection**: Check database credentials in config
2. **Permissions**: Ensure proper file permissions
3. **Email Issues**: Verify SMTP settings
4. **Session Problems**: Check PHP session configuration

## 📈 Roadmap

Future enhancements planned:
- Mobile app integration
- Advanced reporting
- Inventory management
- Class scheduling
- Member portal
- API development
- Multi-location support

## 🏗️ Technical Architecture

### Backend
- **PHP 7.4+** with object-oriented design
- **MySQL** with optimized queries
- **PDO** for database abstraction
- **Session-based** authentication

### Frontend
- **Tailwind CSS** for styling
- **Alpine.js** for interactivity
- **Chart.js** for analytics
- **Font Awesome** for icons

### Security
- **CSRF tokens** on all forms
- **Prepared statements** for SQL
- **Password hashing** with bcrypt
- **Role-based permissions**

## 📄 License

This project is proprietary software. All rights reserved.

## 🙏 Credits

Built with modern web technologies:
- [Tailwind CSS](https://tailwindcss.com/)
- [Alpine.js](https://alpinejs.dev/)
- [Chart.js](https://www.chartjs.org/)
- [Font Awesome](https://fontawesome.com/)

---

**MyGym Management System** - Professional gym management made simple.
