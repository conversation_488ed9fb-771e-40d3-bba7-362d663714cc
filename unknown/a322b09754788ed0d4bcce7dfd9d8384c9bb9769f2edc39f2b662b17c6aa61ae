<?php
/**
 * Manual System Reset
 * MyGym Management System
 */

session_start();

echo "<h2>🔄 Manual System Reset</h2>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .info { color: blue; } .warning { color: orange; }</style>";

// Clear all session data
session_destroy();
session_start();
echo "<p class='success'>✅ All session data cleared</p>";

// Remove config file
if (file_exists('config/config.php')) {
    if (unlink('config/config.php')) {
        echo "<p class='success'>✅ Configuration file deleted</p>";
    } else {
        echo "<p class='error'>❌ Failed to delete config file - check file permissions</p>";
    }
} else {
    echo "<p class='info'>ℹ️ Configuration file already removed</p>";
}

// Clear database tables
echo "<h3>🗑️ Database Cleanup</h3>";
try {
    $config = [
        'db_host' => 'localhost',
        'db_name' => 'mygym',
        'db_username' => 'root',
        'db_password' => ''
    ];
    
    // Connect to MySQL server
    $dsn = "mysql:host={$config['db_host']};charset=utf8mb4";
    $pdo = new PDO($dsn, $config['db_username'], $config['db_password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p class='success'>✅ Connected to MySQL server</p>";
    
    // Check if database exists
    $databases = $pdo->query("SHOW DATABASES LIKE '{$config['db_name']}'")->fetchAll();
    if (!empty($databases)) {
        // Connect to the specific database
        $dsn = "mysql:host={$config['db_host']};dbname={$config['db_name']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['db_username'], $config['db_password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Get all tables
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($tables)) {
            // Drop all tables
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
            foreach ($tables as $table) {
                $pdo->exec("DROP TABLE IF EXISTS `$table`");
            }
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
            
            echo "<p class='success'>✅ Dropped " . count($tables) . " tables: " . implode(', ', $tables) . "</p>";
        } else {
            echo "<p class='info'>ℹ️ No tables found in database</p>";
        }
        
        // Optionally drop the entire database
        // $pdo->exec("DROP DATABASE IF EXISTS `{$config['db_name']}`");
        // echo "<p class='success'>✅ Database '{$config['db_name']}' dropped</p>";
        
    } else {
        echo "<p class='info'>ℹ️ Database '{$config['db_name']}' doesn't exist</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='warning'>⚠️ Database cleanup failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p class='info'>This is normal if the database doesn't exist yet.</p>";
}

echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 20px; margin: 20px 0;'>";
echo "<h3 style='color: #155724; margin-top: 0;'>🎉 Manual Reset Complete!</h3>";
echo "<p style='color: #155724;'>The system has been completely reset. You can now run a fresh installation.</p>";
echo "</div>";

echo "<h3>🚀 Next Steps:</h3>";
echo "<a href='debug_install.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔧 Debug Installation</a>";
echo "<a href='auto_install.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>⚡ Auto Install</a>";
echo "<a href='install.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🧙‍♂️ Manual Install</a>";

echo "<h3>📋 What Was Reset:</h3>";
echo "<ul>";
echo "<li>✅ All session data cleared</li>";
echo "<li>✅ Configuration file removed</li>";
echo "<li>✅ Database tables dropped (if they existed)</li>";
echo "<li>✅ System ready for fresh installation</li>";
echo "</ul>";
?>
