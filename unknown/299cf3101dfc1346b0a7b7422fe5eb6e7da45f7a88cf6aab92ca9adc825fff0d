<?php
/**
 * MyGym Management System - Installation Wizard
 * Modern 4-step installation process
 */

session_start();

// Check if already installed
if (file_exists('config/config.php')) {
    header('Location: index.php');
    exit;
}

$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$errors = [];
$success = [];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1:
            $result = handleDatabaseSetup();
            if ($result['success']) {
                header('Location: install.php?step=2');
                exit;
            } else {
                $errors = $result['errors'];
            }
            break;
            
        case 2:
            $result = handleSiteSettings();
            if ($result['success']) {
                header('Location: install.php?step=3');
                exit;
            } else {
                $errors = $result['errors'];
            }
            break;
            
        case 3:
            $result = handleAdminSetup();
            if ($result['success']) {
                header('Location: install.php?step=4');
                exit;
            } else {
                $errors = $result['errors'];
            }
            break;
            
        case 4:
            $result = handleDatabaseInstall();
            if ($result['success']) {
                $success[] = 'Installation completed successfully!';
            } else {
                $errors = $result['errors'];
            }
            break;
    }
}

function handleDatabaseSetup() {
    $host = trim($_POST['db_host'] ?? '');
    $name = trim($_POST['db_name'] ?? '');
    $username = trim($_POST['db_username'] ?? '');
    $password = $_POST['db_password'] ?? '';
    
    $errors = [];
    
    if (empty($host)) $errors[] = 'Database host is required';
    if (empty($name)) $errors[] = 'Database name is required';
    if (empty($username)) $errors[] = 'Database username is required';
    
    if (!empty($errors)) {
        return ['success' => false, 'errors' => $errors];
    }
    
    // Test database connection
    try {
        $dsn = "mysql:host={$host};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Check if database exists, create if not
        $stmt = $pdo->prepare("CREATE DATABASE IF NOT EXISTS `{$name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $stmt->execute();
        
        // Store database config in session
        $_SESSION['install_db'] = [
            'host' => $host,
            'name' => $name,
            'username' => $username,
            'password' => $password
        ];
        
        return ['success' => true];
    } catch (PDOException $e) {
        return ['success' => false, 'errors' => ['Database connection failed: ' . $e->getMessage()]];
    }
}

function handleSiteSettings() {
    $gymName = trim($_POST['gym_name'] ?? '');
    $siteUrl = trim($_POST['site_url'] ?? '');
    $currency = trim($_POST['currency'] ?? 'USD');
    $language = trim($_POST['language'] ?? 'en');
    
    $errors = [];
    
    if (empty($gymName)) $errors[] = 'Gym name is required';
    if (empty($siteUrl)) $errors[] = 'Site URL is required';
    
    if (!empty($errors)) {
        return ['success' => false, 'errors' => $errors];
    }
    
    $_SESSION['install_site'] = [
        'gym_name' => $gymName,
        'site_url' => rtrim($siteUrl, '/'),
        'currency' => $currency,
        'language' => $language
    ];
    
    return ['success' => true];
}

function handleAdminSetup() {
    $name = trim($_POST['admin_name'] ?? '');
    $email = trim($_POST['admin_email'] ?? '');
    $password = $_POST['admin_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    $errors = [];
    
    if (empty($name)) $errors[] = 'Admin name is required';
    if (empty($email)) $errors[] = 'Admin email is required';
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'Valid email is required';
    if (empty($password)) $errors[] = 'Password is required';
    if (strlen($password) < 6) $errors[] = 'Password must be at least 6 characters';
    if ($password !== $confirmPassword) $errors[] = 'Passwords do not match';
    
    if (!empty($errors)) {
        return ['success' => false, 'errors' => $errors];
    }
    
    $_SESSION['install_admin'] = [
        'name' => $name,
        'email' => $email,
        'password' => password_hash($password, PASSWORD_DEFAULT)
    ];

    // Store email for step 4 display
    $_SESSION['install_admin_email'] = $email;

    return ['success' => true];
}

function handleDatabaseInstall() {
    try {
        // Create config file
        $dbConfig = $_SESSION['install_db'];
        $siteConfig = $_SESSION['install_site'];
        $adminConfig = $_SESSION['install_admin'];
        
        $configContent = "<?php\nreturn [\n";
        $configContent .= "    'database' => [\n";
        $configContent .= "        'host' => '{$dbConfig['host']}',\n";
        $configContent .= "        'name' => '{$dbConfig['name']}',\n";
        $configContent .= "        'username' => '{$dbConfig['username']}',\n";
        $configContent .= "        'password' => '{$dbConfig['password']}'\n";
        $configContent .= "    ],\n";
        $configContent .= "    'app' => [\n";
        $configContent .= "        'name' => '{$siteConfig['gym_name']}',\n";
        $configContent .= "        'url' => '{$siteConfig['site_url']}',\n";
        $configContent .= "        'currency' => '{$siteConfig['currency']}',\n";
        $configContent .= "        'language' => '{$siteConfig['language']}'\n";
        $configContent .= "    ]\n";
        $configContent .= "];\n";
        
        if (!is_dir('config')) {
            mkdir('config', 0755, true);
        }
        
        file_put_contents('config/config.php', $configContent);
        
        // Connect to database and run schema
        $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['name']};charset=utf8mb4";
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Execute schema
        $schema = file_get_contents('schema.sql');
        $pdo->exec($schema);
        
        // Insert admin user
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, is_active) VALUES (?, ?, ?, 'admin', 1)");
        $stmt->execute([$adminConfig['name'], $adminConfig['email'], $adminConfig['password']]);
        
        // Insert site settings
        $settings = [
            'gym_name' => $siteConfig['gym_name'],
            'site_url' => $siteConfig['site_url'],
            'currency' => $siteConfig['currency'],
            'language' => $siteConfig['language'],
            'timezone' => 'UTC',
            'date_format' => 'Y-m-d',
            'time_format' => 'H:i'
        ];
        
        foreach ($settings as $key => $value) {
            $stmt = $pdo->prepare("INSERT INTO config (setting_key, setting_value) VALUES (?, ?)");
            $stmt->execute([$key, $value]);
        }
        
        // Clear session
        unset($_SESSION['install_db'], $_SESSION['install_site'], $_SESSION['install_admin']);
        
        return ['success' => true];
    } catch (Exception $e) {
        return ['success' => false, 'errors' => ['Installation failed: ' . $e->getMessage()]];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MyGym Installation Wizard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .step-active { background-color: #2563eb; color: white; }
        .step-completed { background-color: #16a34a; color: white; }
        .step-inactive { background-color: #e5e7eb; color: #4b5563; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">
                    <i class="fas fa-dumbbell text-blue-600"></i>
                    MyGym Installation
                </h1>
                <p class="text-gray-600">Set up your gym management system in 4 easy steps</p>
            </div>
            
            <!-- Progress Steps -->
            <div class="flex justify-center mb-8">
                <div class="flex items-center space-x-4">
                    <?php for ($i = 1; $i <= 4; $i++): ?>
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold
                                <?php 
                                    if ($i < $step) echo 'step-completed';
                                    elseif ($i == $step) echo 'step-active';
                                    else echo 'step-inactive';
                                ?>">
                                <?php if ($i < $step): ?>
                                    <i class="fas fa-check"></i>
                                <?php else: ?>
                                    <?= $i ?>
                                <?php endif; ?>
                            </div>
                            <?php if ($i < 4): ?>
                                <div class="w-16 h-1 mx-2 <?= $i < $step ? 'bg-green-600' : 'bg-gray-200' ?>"></div>
                            <?php endif; ?>
                        </div>
                    <?php endfor; ?>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="bg-white rounded-2xl shadow-lg p-8">
                <?php if (!empty($errors)): ?>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <div class="flex">
                            <i class="fas fa-exclamation-triangle text-red-400 mt-1 mr-3"></i>
                            <div>
                                <h3 class="text-red-800 font-medium">Please fix the following errors:</h3>
                                <ul class="mt-2 text-red-700 list-disc list-inside">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?= htmlspecialchars($error) ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($success)): ?>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                        <div class="flex">
                            <i class="fas fa-check-circle text-green-400 mt-1 mr-3"></i>
                            <div>
                                <?php foreach ($success as $msg): ?>
                                    <p class="text-green-800"><?= htmlspecialchars($msg) ?></p>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php
                // Include step content inline
                switch ($step) {
                    case 1:
                        include_once 'install/step1.php';
                        break;
                    case 2:
                        include_once 'install/step2.php';
                        break;
                    case 3:
                        include_once 'install/step3.php';
                        break;
                    case 4:
                        include_once 'install/step4.php';
                        break;
                }
                ?>
            </div>
        </div>
    </div>
</body>
</html>
