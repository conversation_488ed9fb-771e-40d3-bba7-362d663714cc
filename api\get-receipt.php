<?php
/**
 * Get Receipt Data API
 * MyGym Management System
 */

// Prevent any output before JSON
ob_start();
ob_clean();

// Set JSON header
header('Content-Type: application/json');

// Start session
session_start();

// Get payment ID
$paymentId = $_GET['id'] ?? null;
if (!$paymentId || !is_numeric($paymentId)) {
    echo json_encode(['success' => false, 'message' => 'Invalid payment ID']);
    exit;
}

try {
    // Include database config
    $config = require_once '../config/config.php';

    // Create PDO connection
    $pdo = new PDO(
        "mysql:host={$config['database']['host']};dbname={$config['database']['name']};charset=utf8mb4",
        $config['database']['username'],
        $config['database']['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Get payment
    $stmt = $pdo->prepare("SELECT * FROM payments WHERE id = ?");
    $stmt->execute([$paymentId]);
    $payment = $stmt->fetch();

    if (!$payment) {
        echo json_encode(['success' => false, 'message' => 'Payment not found']);
        exit;
    }

    // Get member
    $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
    $stmt->execute([$payment['member_id']]);
    $member = $stmt->fetch();

    if (!$member) {
        echo json_encode(['success' => false, 'message' => 'Member not found']);
        exit;
    }

    // Get plan
    $stmt = $pdo->prepare("SELECT * FROM plans WHERE id = ?");
    $stmt->execute([$payment['plan_id']]);
    $plan = $stmt->fetch();

    // Get user (if exists)
    $processedByName = 'Local Admin';
    if ($payment['processed_by']) {
        $stmt = $pdo->prepare("SELECT name FROM users WHERE id = ?");
        $stmt->execute([$payment['processed_by']]);
        $user = $stmt->fetch();
        if ($user) {
            $processedByName = $user['name'];
        }
    }

    // Get gym settings
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM config WHERE setting_key IN ('gym_name', 'gym_address', 'gym_phone', 'gym_email', 'currency')");
    $stmt->execute();
    $settings = $stmt->fetchAll();

    $gymSettings = [];
    foreach ($settings as $setting) {
        $gymSettings[$setting['setting_key']] = $setting['setting_value'];
    }

    // Currency symbols
    $currencySymbols = [
        'USD' => '$',
        'EUR' => '€',
        'GBP' => '£',
        'INR' => '₹',
        'JPY' => '¥'
    ];

    $currency = $gymSettings['currency'] ?? 'USD';
    $currencySymbol = $currencySymbols[$currency] ?? '$';

    // Prepare receipt data
    $receiptData = [
        'id' => $payment['id'],
        'receipt_number' => $payment['receipt_number'],
        'member_name' => $member['first_name'] . ' ' . $member['last_name'],
        'member_id' => $member['member_id'],
        'phone' => $member['phone'],
        'email' => $member['email'],
        'plan_name' => $plan ? $plan['name'] : 'Unknown Plan',
        'duration_months' => $plan ? $plan['duration_months'] : 0,
        'amount' => $payment['amount'],
        'currency_symbol' => $currencySymbol,
        'payment_method' => $payment['payment_method'],
        'transaction_id' => $payment['transaction_id'],
        'payment_date' => $payment['payment_date'],
        'start_date' => $payment['start_date'],
        'end_date' => $payment['end_date'],
        'notes' => $payment['notes'],
        'processed_by_name' => $processedByName,
        'created_at' => $payment['created_at'],
        'gym_name' => $gymSettings['gym_name'] ?? 'MyGym',
        'gym_address' => $gymSettings['gym_address'] ?? '',
        'gym_phone' => $gymSettings['gym_phone'] ?? '',
        'gym_email' => $gymSettings['gym_email'] ?? ''
    ];

    echo json_encode([
        'success' => true,
        'receipt' => $receiptData
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
