<?php
/**
 * Get Receipt Data API
 * MyGym Management System
 */

session_start();
require_once '../includes/Database.php';
require_once '../includes/Auth.php';
require_once '../includes/Config.php';

// Set JSON header
header('Content-Type: application/json');

// Check authentication
if (!Auth::check()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Get payment ID
$paymentId = $_GET['id'] ?? null;
if (!$paymentId || !is_numeric($paymentId)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid payment ID']);
    exit;
}

try {
    $db = new Database();
    
    // Get payment details with member and plan information
    $payment = $db->fetch("
        SELECT p.*, 
               CONCAT(m.first_name, ' ', m.last_name) as member_name,
               m.member_id,
               m.phone,
               m.email,
               pl.name as plan_name,
               pl.duration_months,
               u.name as processed_by_name
        FROM payments p
        JOIN members m ON p.member_id = m.id
        LEFT JOIN plans pl ON p.plan_id = pl.id
        LEFT JOIN users u ON p.processed_by = u.id
        WHERE p.id = ?
    ", [$paymentId]);
    
    if (!$payment) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Payment not found']);
        exit;
    }
    
    // Get gym settings
    $gymName = Config::get('gym_name', 'MyGym');
    $gymAddress = Config::get('gym_address', '');
    $gymPhone = Config::get('gym_phone', '');
    $gymEmail = Config::get('gym_email', '');
    $currency = Config::get('currency', 'USD');
    
    // Currency symbols
    $currencySymbols = [
        'USD' => '$',
        'EUR' => '€',
        'GBP' => '£',
        'INR' => '₹',
        'JPY' => '¥'
    ];
    
    $currencySymbol = $currencySymbols[$currency] ?? $currency . ' ';
    
    // Prepare receipt data
    $receiptData = [
        'id' => $payment['id'],
        'receipt_number' => $payment['receipt_number'],
        'member_name' => $payment['member_name'],
        'member_id' => $payment['member_id'],
        'phone' => $payment['phone'],
        'email' => $payment['email'],
        'plan_name' => $payment['plan_name'],
        'duration_months' => $payment['duration_months'],
        'amount' => $payment['amount'],
        'currency_symbol' => $currencySymbol,
        'payment_method' => $payment['payment_method'],
        'transaction_id' => $payment['transaction_id'],
        'payment_date' => $payment['payment_date'],
        'start_date' => $payment['start_date'],
        'end_date' => $payment['end_date'],
        'notes' => $payment['notes'],
        'processed_by_name' => $payment['processed_by_name'],
        'created_at' => $payment['created_at'],
        'gym_name' => $gymName,
        'gym_address' => $gymAddress,
        'gym_phone' => $gymPhone,
        'gym_email' => $gymEmail
    ];
    
    echo json_encode([
        'success' => true,
        'receipt' => $receiptData
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Error retrieving receipt data: ' . $e->getMessage()
    ]);
}
?>
