<?php
/**
 * Activity Logger Class
 * MyGym Management System
 */

class ActivityLogger {
    
    /**
     * Log an activity
     */
    public static function log($action, $table = null, $record_id = null, $old_data = null, $new_data = null) {
        try {
            $db = Database::getInstance();

            // Get user ID safely - handle case where Auth class might not be available yet
            $user_id = null;
            if (class_exists('Auth') && method_exists('Auth', 'id')) {
                $authId = Auth::id();
                // Check if this is a local admin (string ID) vs database user (numeric ID)
                if (is_numeric($authId)) {
                    $user_id = $authId;
                } else {
                    // Local admin - set to null since they don't exist in users table
                    $user_id = null;
                }
            } elseif (isset($_SESSION['user_id'])) {
                $sessionUserId = $_SESSION['user_id'];
                // Check if this is a local admin session
                if (is_numeric($sessionUserId)) {
                    $user_id = $sessionUserId;
                } else {
                    // Local admin session - set to null
                    $user_id = null;
                }
            }

            $data = [
                'user_id' => $user_id,
                'action' => $action,
                'table_name' => $table,
                'record_id' => $record_id,
                'old_data' => $old_data ? json_encode($old_data) : null,
                'new_data' => $new_data ? json_encode($new_data) : null,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $db->insert('activity_logs', $data);

        } catch (Exception $e) {
            // Log errors silently to avoid breaking the main functionality
            error_log('ActivityLogger Error: ' . $e->getMessage());
        }
    }
    
    /**
     * Get recent activities
     */
    public static function getRecent($limit = 50) {
        try {
            $db = Database::getInstance();
            
            return $db->fetchAll("
                SELECT al.*, u.name as user_name
                FROM activity_logs al
                LEFT JOIN users u ON al.user_id = u.id
                ORDER BY al.created_at DESC
                LIMIT ?
            ", [$limit]);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Get activities for a specific record
     */
    public static function getForRecord($table, $record_id, $limit = 20) {
        try {
            $db = Database::getInstance();
            
            return $db->fetchAll("
                SELECT al.*, u.name as user_name
                FROM activity_logs al
                LEFT JOIN users u ON al.user_id = u.id
                WHERE al.table_name = ? AND al.record_id = ?
                ORDER BY al.created_at DESC
                LIMIT ?
            ", [$table, $record_id, $limit]);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Get activities for a specific user
     */
    public static function getForUser($user_id, $limit = 50) {
        try {
            $db = Database::getInstance();
            
            return $db->fetchAll("
                SELECT al.*, u.name as user_name
                FROM activity_logs al
                LEFT JOIN users u ON al.user_id = u.id
                WHERE al.user_id = ?
                ORDER BY al.created_at DESC
                LIMIT ?
            ", [$user_id, $limit]);
            
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Clean old logs (older than specified days)
     */
    public static function cleanup($days = 90) {
        try {
            $db = Database::getInstance();
            
            $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            
            $stmt = $db->query("DELETE FROM activity_logs WHERE created_at < ?", [$cutoffDate]);
            $deleted = $stmt->rowCount();
            
            return $deleted;
            
        } catch (Exception $e) {
            return false;
        }
    }
}
