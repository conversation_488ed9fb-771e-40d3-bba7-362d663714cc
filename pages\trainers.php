<?php
/**
 * Trainers Management Page
 * MyGym Management System
 */

session_start();
require_once '../config/database.php';
require_once '../includes/auth.php';

// Require authentication and permission
Auth::requireAuth();
Auth::requirePermission('trainers.view');

$db = Database::getInstance();
$action = $_GET['action'] ?? 'list';
$trainerId = $_GET['id'] ?? null;
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add' || $action === 'edit') {
        $result = handleTrainerSave();
        if ($result['success']) {
            Session::success($result['message']);
            header('Location: trainers.php');
            exit;
        } else {
            $error = $result['message'];
        }
    } elseif ($action === 'delete') {
        $result = handleTrainerDelete();
        if ($result['success']) {
            Session::success($result['message']);
        } else {
            Session::error($result['message']);
        }
        header('Location: trainers.php');
        exit;
    }
}

function handleTrainerSave() {
    global $db, $action, $trainerId;
    
    // Validate input
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $specialty = trim($_POST['specialty'] ?? '');
    $bio = trim($_POST['bio'] ?? '');
    $hourlyRate = $_POST['hourly_rate'] ?? null;
    
    if (empty($name)) {
        return ['success' => false, 'message' => 'Trainer name is required.'];
    }
    
    if ($email && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return ['success' => false, 'message' => 'Please enter a valid email address.'];
    }
    
    if ($hourlyRate && (!is_numeric($hourlyRate) || $hourlyRate < 0)) {
        return ['success' => false, 'message' => 'Please enter a valid hourly rate.'];
    }
    
    try {
        $db->beginTransaction();
        
        $data = [
            'name' => $name,
            'email' => $email ?: null,
            'phone' => $phone ?: null,
            'specialty' => $specialty ?: null,
            'bio' => $bio ?: null,
            'hourly_rate' => $hourlyRate ?: null
        ];
        
        if ($action === 'add') {
            $data['is_active'] = 1;
            $newTrainerId = $db->insert('trainers', $data);
            
            // Log activity
            ActivityLogger::log('Trainer Created', 'trainers', $newTrainerId, null, $data);
            
            $message = 'Trainer added successfully.';
        } else {
            // Update existing trainer
            $oldData = $db->fetch("SELECT * FROM trainers WHERE id = ?", [$trainerId]);
            $db->update('trainers', $data, 'id = ?', [$trainerId]);
            
            // Log activity
            ActivityLogger::log('Trainer Updated', 'trainers', $trainerId, $oldData, $data);
            
            $message = 'Trainer updated successfully.';
        }
        
        $db->commit();
        return ['success' => true, 'message' => $message];
        
    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'message' => 'Failed to save trainer: ' . $e->getMessage()];
    }
}

function handleTrainerDelete() {
    global $db;
    
    $trainerId = $_POST['trainer_id'] ?? null;
    if (!$trainerId) {
        return ['success' => false, 'message' => 'Trainer ID is required.'];
    }
    
    try {
        $trainer = $db->fetch("SELECT * FROM trainers WHERE id = ?", [$trainerId]);
        if (!$trainer) {
            return ['success' => false, 'message' => 'Trainer not found.'];
        }
        
        // Check if trainer has assigned members
        $assignedMembers = $db->fetch("SELECT COUNT(*) as count FROM members WHERE trainer_id = ?", [$trainerId])['count'];
        if ($assignedMembers > 0) {
            return ['success' => false, 'message' => 'Cannot delete trainer with assigned members. Please reassign members first.'];
        }
        
        $db->beginTransaction();
        
        // Soft delete trainer
        $db->update('trainers', ['is_active' => 0], 'id = ?', [$trainerId]);
        
        // Log activity
        ActivityLogger::log('Trainer Deleted', 'trainers', $trainerId, $trainer);
        
        $db->commit();
        return ['success' => true, 'message' => 'Trainer deleted successfully.'];
        
    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'message' => 'Failed to delete trainer: ' . $e->getMessage()];
    }
}

// Get trainer data for edit
$trainer = null;
if ($action === 'edit' && $trainerId) {
    $trainer = $db->fetch("SELECT * FROM trainers WHERE id = ?", [$trainerId]);
    if (!$trainer) {
        Session::error('Trainer not found.');
        header('Location: trainers.php');
        exit;
    }
}

// Get trainers list with pagination and search
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;
$search = trim($_GET['search'] ?? '');
$statusFilter = $_GET['status'] ?? '';

$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(name LIKE ? OR email LIKE ? OR phone LIKE ? OR specialty LIKE ?)";
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
}

if ($statusFilter !== '') {
    $whereConditions[] = "is_active = ?";
    $params[] = $statusFilter;
}

$whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get total count
$totalTrainers = $db->fetch("SELECT COUNT(*) as count FROM trainers $whereClause", $params)['count'];
$totalPages = ceil($totalTrainers / $limit);

// Get trainers with member count
$trainers = $db->fetchAll("
    SELECT t.*, 
           COUNT(m.id) as assigned_members
    FROM trainers t
    LEFT JOIN members m ON t.id = m.trainer_id AND m.status = 'active'
    $whereClause
    GROUP BY t.id
    ORDER BY t.created_at DESC
    LIMIT $limit OFFSET $offset
", $params);

$pageTitle = 'Trainers';
include '../includes/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Trainers</h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Manage your gym trainers and specialists</p>
        </div>
        
        <?php if (Auth::can('trainers.create')): ?>
        <div class="mt-4 sm:mt-0">
            <a href="trainers.php?action=add" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                <i class="fas fa-plus mr-2"></i>
                Add Trainer
            </a>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Trainers</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($totalTrainers) ?></p>
                </div>
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-user-tie text-blue-600 dark:text-blue-400"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Trainers</p>
                    <?php
                    $activeTrainers = $db->fetch("SELECT COUNT(*) as count FROM trainers WHERE is_active = 1")['count'];
                    ?>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($activeTrainers) ?></p>
                </div>
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Assigned Members</p>
                    <?php
                    $assignedMembers = $db->fetch("SELECT COUNT(*) as count FROM members WHERE trainer_id IS NOT NULL AND status = 'active'")['count'];
                    ?>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($assignedMembers) ?></p>
                </div>
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-users text-purple-600 dark:text-purple-400"></i>
                </div>
            </div>
        </div>
    </div>

    <?php if ($action === 'list'): ?>
    <!-- Filters and Search -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                <input type="text"
                       name="search"
                       value="<?= htmlspecialchars($search) ?>"
                       placeholder="Search trainers..."
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                <select name="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option value="">All Status</option>
                    <option value="1" <?= $statusFilter === '1' ? 'selected' : '' ?>>Active</option>
                    <option value="0" <?= $statusFilter === '0' ? 'selected' : '' ?>>Inactive</option>
                </select>
            </div>

            <div class="flex items-end space-x-2">
                <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-search mr-2"></i>Filter
                </button>
                <a href="trainers.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </form>
    </div>

    <!-- Trainers Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php foreach ($trainers as $trainerRow): ?>
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-start justify-between mb-4">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <?php if ($trainerRow['avatar']): ?>
                            <img src="<?= htmlspecialchars($trainerRow['avatar']) ?>" alt="" class="w-12 h-12 rounded-full object-cover">
                        <?php else: ?>
                            <i class="fas fa-user-tie text-blue-600 dark:text-blue-400"></i>
                        <?php endif; ?>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                            <?= htmlspecialchars($trainerRow['name']) ?>
                        </h3>
                        <?php if ($trainerRow['specialty']): ?>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                <?= htmlspecialchars($trainerRow['specialty']) ?>
                            </p>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    <?php if ($trainerRow['is_active']): ?>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Active
                        </span>
                    <?php else: ?>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                            Inactive
                        </span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="space-y-2 mb-4">
                <?php if ($trainerRow['email']): ?>
                    <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-envelope mr-2"></i>
                        <?= htmlspecialchars($trainerRow['email']) ?>
                    </div>
                <?php endif; ?>

                <?php if ($trainerRow['phone']): ?>
                    <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-phone mr-2"></i>
                        <?= htmlspecialchars($trainerRow['phone']) ?>
                    </div>
                <?php endif; ?>

                <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <i class="fas fa-users mr-2"></i>
                    <?= $trainerRow['assigned_members'] ?> assigned members
                </div>

                <?php if ($trainerRow['hourly_rate']): ?>
                    <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-dollar-sign mr-2"></i>
                        <?= formatCurrency($trainerRow['hourly_rate']) ?>/hour
                    </div>
                <?php endif; ?>
            </div>

            <?php if ($trainerRow['bio']): ?>
                <div class="mb-4">
                    <p class="text-sm text-gray-600 dark:text-gray-400 line-clamp-3">
                        <?= htmlspecialchars($trainerRow['bio']) ?>
                    </p>
                </div>
            <?php endif; ?>

            <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                <div class="flex items-center space-x-2">
                    <?php if (Auth::can('trainers.edit')): ?>
                        <a href="trainers.php?action=edit&id=<?= $trainerRow['id'] ?>"
                           class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                            <i class="fas fa-edit"></i>
                        </a>
                    <?php endif; ?>

                    <a href="members.php?trainer=<?= $trainerRow['id'] ?>"
                       class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                       title="View assigned members">
                        <i class="fas fa-users"></i>
                    </a>

                    <?php if (Auth::can('trainers.delete') && $trainerRow['assigned_members'] == 0): ?>
                        <button onclick="deleteTrainer(<?= $trainerRow['id'] ?>, '<?= htmlspecialchars($trainerRow['name']) ?>')"
                                class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                title="Delete Trainer">
                            <i class="fas fa-trash"></i>
                        </button>
                    <?php endif; ?>
                </div>

                <span class="text-xs text-gray-500 dark:text-gray-400">
                    Added <?= timeAgo($trainerRow['created_at']) ?>
                </span>
            </div>
        </div>
        <?php endforeach; ?>

        <?php if (empty($trainers)): ?>
        <div class="col-span-full">
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-12 text-center shadow-sm border border-gray-100 dark:border-gray-700">
                <i class="fas fa-user-tie text-4xl text-gray-400 mb-4"></i>
                <p class="text-lg font-medium text-gray-500 dark:text-gray-400">No trainers found</p>
                <p class="mt-1 text-gray-400 dark:text-gray-500">Get started by adding your first trainer.</p>
                <?php if (Auth::can('trainers.create')): ?>
                    <a href="trainers.php?action=add"
                       class="inline-flex items-center mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Add First Trainer
                    </a>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php if ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit Trainer Form -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                <?= $action === 'add' ? 'Add New Trainer' : 'Edit Trainer' ?>
            </h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                <?= $action === 'add' ? 'Fill in the trainer details below.' : 'Update the trainer information.' ?>
            </p>
        </div>

        <?php if ($error): ?>
            <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex">
                    <i class="fas fa-exclamation-circle text-red-400 mt-0.5 mr-3"></i>
                    <span class="text-red-800"><?= htmlspecialchars($error) ?></span>
                </div>
            </div>
        <?php endif; ?>

        <form method="POST" enctype="multipart/form-data" class="space-y-6">
            <?= Auth::csrfField() ?>

            <!-- Basic Information -->
            <div>
                <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Basic Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Full Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               id="name"
                               name="name"
                               value="<?= htmlspecialchars($trainer['name'] ?? $_POST['name'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               required>
                    </div>

                    <div>
                        <label for="specialty" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Specialty</label>
                        <input type="text"
                               id="specialty"
                               name="specialty"
                               value="<?= htmlspecialchars($trainer['specialty'] ?? $_POST['specialty'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               placeholder="e.g., Weight Training, Yoga, Cardio">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email</label>
                        <input type="email"
                               id="email"
                               name="email"
                               value="<?= htmlspecialchars($trainer['email'] ?? $_POST['email'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>

                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Phone</label>
                        <input type="tel"
                               id="phone"
                               name="phone"
                               value="<?= htmlspecialchars($trainer['phone'] ?? $_POST['phone'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>

                    <div>
                        <label for="hourly_rate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Hourly Rate</label>
                        <input type="number"
                               id="hourly_rate"
                               name="hourly_rate"
                               value="<?= htmlspecialchars($trainer['hourly_rate'] ?? $_POST['hourly_rate'] ?? '') ?>"
                               step="0.01"
                               min="0"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               placeholder="0.00">
                    </div>
                </div>
            </div>

            <!-- Bio -->
            <div>
                <label for="bio" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Bio</label>
                <textarea id="bio"
                          name="bio"
                          rows="4"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                          placeholder="Tell us about the trainer's experience, certifications, and expertise..."><?= htmlspecialchars($trainer['bio'] ?? $_POST['bio'] ?? '') ?></textarea>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <a href="trainers.php"
                   class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition duration-200">
                    Cancel
                </a>
                <button type="submit"
                        class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                    <i class="fas fa-save mr-2"></i>
                    <?= $action === 'add' ? 'Add Trainer' : 'Update Trainer' ?>
                </button>
            </div>
        </form>
    </div>
    <?php endif; ?>
</div>

<script>
function deleteTrainer(trainerId, trainerName) {
    showConfirmModal({
        title: 'Delete Trainer',
        message: `Are you sure you want to delete trainer <strong>${trainerName}</strong>?<br><br>This action cannot be undone.`,
        confirmText: 'Delete Trainer',
        cancelText: 'Cancel',
        type: 'danger',
        onConfirm: () => {
            // Create and submit form
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            form.innerHTML = `
                <input type="hidden" name="trainer_id" value="${trainerId}">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="csrf_token" value="<?= Auth::generateCsrfToken() ?>">
            `;

            document.body.appendChild(form);
            form.submit();
        }
    });
}
</script>

<?php include '../includes/footer.php'; ?>
