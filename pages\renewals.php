<?php
/**
 * Membership Renewals Page
 * MyGym Management System
 */

session_start();
require_once '../config/database.php';
require_once '../includes/auth.php';

// Require authentication and permission
Auth::requireAuth();
Auth::requirePermission('payments.view');

$db = Database::getInstance();
$action = $_GET['action'] ?? 'list';
$memberId = $_GET['member_id'] ?? null;
$error = '';
$success = '';

// Handle AJAX quick renewal
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action === 'quick_renew') {
    header('Content-Type: application/json');

    // Verify CSRF token
    $csrfToken = $_POST['csrf_token'] ?? '';
    if (!Auth::verifyCsrfToken($csrfToken)) {
        echo json_encode(['success' => false, 'message' => 'Invalid security token.']);
        exit;
    }

    $memberId = $_POST['member_id'] ?? null;
    $planId = $_POST['plan_id'] ?? null;
    $amount = $_POST['amount'] ?? null;

    if (!$memberId || !$planId || !$amount) {
        echo json_encode(['success' => false, 'message' => 'Missing required data.']);
        exit;
    }

    $result = handleQuickRenewal($memberId, $planId, $amount);
    echo json_encode($result);
    exit;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'renew') {
        $result = handleRenewal();
        if ($result['success']) {
            Session::success($result['message']);
            header('Location: renewals.php');
            exit;
        } else {
            $error = $result['message'];
        }
    } elseif ($action === 'bulk_renew') {
        $result = handleBulkRenewal();
        if ($result['success']) {
            Session::success($result['message']);
        } else {
            Session::error($result['message']);
        }
        header('Location: renewals.php');
        exit;
    }
}

function handleQuickRenewal($memberId, $planId, $amount) {
    global $db;

    if (!is_numeric($amount) || $amount <= 0) {
        return ['success' => false, 'message' => 'Please enter a valid amount.'];
    }

    try {
        $db->beginTransaction();

        // Get member and plan details
        $member = $db->fetch("SELECT * FROM members WHERE id = ?", [$memberId]);
        $plan = $db->fetch("SELECT * FROM plans WHERE id = ?", [$planId]);

        if (!$member || !$plan) {
            throw new Exception('Member or plan not found.');
        }

        // Generate receipt number
        $receiptNumber = 'QRN' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);

        // Calculate renewal dates - extend from current end_date or today if expired
        $currentEndDate = $member['end_date'];
        $paymentDate = date('Y-m-d');
        $startDate = ($currentEndDate && $currentEndDate >= date('Y-m-d'))
            ? $currentEndDate
            : $paymentDate;

        $endDate = date('Y-m-d', strtotime($startDate . ' + ' . $plan['duration_months'] . ' months'));

        // Handle processed_by field for local admin vs database users
        $processedBy = Auth::isLocalAdmin() ? null : Auth::id();

        // Insert renewal payment record
        $paymentData = [
            'member_id' => $memberId,
            'plan_id' => $planId,
            'amount' => $amount,
            'payment_method' => 'cash', // Default to cash for quick renewal
            'transaction_id' => null,
            'receipt_number' => $receiptNumber,
            'payment_date' => $paymentDate,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'notes' => 'Quick membership renewal' . (Auth::isLocalAdmin() ? ' (Local Admin)' : ''),
            'processed_by' => $processedBy
        ];

        $paymentId = $db->insert('payments', $paymentData);

        // Update member's plan and dates
        $memberUpdateData = [
            'plan_id' => $planId,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'status' => 'active'
        ];

        $db->update('members', $memberUpdateData, 'id = ?', [$memberId]);

        // Log activity
        ActivityLogger::log('Quick Membership Renewal', 'payments', $paymentId, null, $paymentData);

        $db->commit();
        return ['success' => true, 'message' => 'Membership renewed successfully. Receipt: ' . $receiptNumber];

    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'message' => 'Failed to renew membership: ' . $e->getMessage()];
    }
}

function handleRenewal() {
    global $db;

    $memberId = $_POST['member_id'] ?? null;
    $planId = $_POST['plan_id'] ?? null;
    $amount = $_POST['amount'] ?? null;
    $paymentMethod = $_POST['payment_method'] ?? 'cash';
    $paymentDate = $_POST['payment_date'] ?? date('Y-m-d');
    $transactionId = trim($_POST['transaction_id'] ?? '');
    $notes = trim($_POST['notes'] ?? '');

    if (!$memberId || !$planId || !$amount) {
        return ['success' => false, 'message' => 'Member, plan, and amount are required.'];
    }

    if (!is_numeric($amount) || $amount <= 0) {
        return ['success' => false, 'message' => 'Please enter a valid amount.'];
    }

    try {
        $db->beginTransaction();

        // Get member and plan details
        $member = $db->fetch("SELECT * FROM members WHERE id = ?", [$memberId]);
        $plan = $db->fetch("SELECT * FROM plans WHERE id = ?", [$planId]);

        if (!$member || !$plan) {
            throw new Exception('Member or plan not found.');
        }

        // Generate receipt number
        $receiptNumber = 'RNW' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);

        // Calculate renewal dates - extend from current end_date or today if expired
        $currentEndDate = $member['end_date'];
        $startDate = ($currentEndDate && $currentEndDate >= date('Y-m-d'))
            ? $currentEndDate
            : $paymentDate;

        $endDate = date('Y-m-d', strtotime($startDate . ' + ' . $plan['duration_months'] . ' months'));

        // Handle processed_by field for local admin vs database users
        $processedBy = null;
        if (Auth::isLocalAdmin()) {
            $processedBy = null;
        } else {
            $processedBy = Auth::id();
        }

        // Insert renewal payment record
        $paymentData = [
            'member_id' => $memberId,
            'plan_id' => $planId,
            'amount' => $amount,
            'payment_method' => $paymentMethod,
            'transaction_id' => $transactionId ?: null,
            'receipt_number' => $receiptNumber,
            'payment_date' => $paymentDate,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'notes' => 'Membership renewal' . ($notes ? ' - ' . $notes : '') . (Auth::isLocalAdmin() ? ' (Local Admin)' : ''),
            'processed_by' => $processedBy
        ];

        $paymentId = $db->insert('payments', $paymentData);

        // Update member's plan and dates
        $memberUpdateData = [
            'plan_id' => $planId,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'status' => 'active'
        ];

        $db->update('members', $memberUpdateData, 'id = ?', [$memberId]);

        // Log activity
        ActivityLogger::log('Membership Renewed', 'payments', $paymentId, null, $paymentData);

        $db->commit();
        return ['success' => true, 'message' => 'Membership renewed successfully. Receipt: ' . $receiptNumber];

    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'message' => 'Failed to renew membership: ' . $e->getMessage()];
    }
}

function handleBulkRenewal() {
    global $db;
    
    $memberIds = $_POST['member_ids'] ?? [];
    $planId = $_POST['bulk_plan_id'] ?? null;
    $paymentMethod = $_POST['bulk_payment_method'] ?? 'cash';
    $paymentDate = $_POST['bulk_payment_date'] ?? date('Y-m-d');
    
    if (empty($memberIds) || !$planId) {
        return ['success' => false, 'message' => 'Please select members and a plan for bulk renewal.'];
    }
    
    try {
        $db->beginTransaction();
        
        $plan = $db->fetch("SELECT * FROM plans WHERE id = ?", [$planId]);
        if (!$plan) {
            throw new Exception('Selected plan not found.');
        }
        
        $renewedCount = 0;
        $processedBy = Auth::isLocalAdmin() ? null : Auth::id();
        
        foreach ($memberIds as $memberId) {
            $member = $db->fetch("SELECT * FROM members WHERE id = ?", [$memberId]);
            if (!$member) continue;
            
            // Generate receipt number
            $receiptNumber = 'BRN' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            // Calculate renewal dates
            $currentEndDate = $member['end_date'];
            $startDate = ($currentEndDate && $currentEndDate >= date('Y-m-d')) 
                ? $currentEndDate 
                : $paymentDate;
            
            $endDate = date('Y-m-d', strtotime($startDate . ' + ' . $plan['duration_months'] . ' months'));
            
            // Insert renewal payment record
            $paymentData = [
                'member_id' => $memberId,
                'plan_id' => $planId,
                'amount' => $plan['price'],
                'payment_method' => $paymentMethod,
                'receipt_number' => $receiptNumber,
                'payment_date' => $paymentDate,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'notes' => 'Bulk membership renewal' . (Auth::isLocalAdmin() ? ' (Local Admin)' : ''),
                'processed_by' => $processedBy
            ];
            
            $db->insert('payments', $paymentData);
            
            // Update member
            $db->update('members', [
                'plan_id' => $planId,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'status' => 'active'
            ], 'id = ?', [$memberId]);
            
            $renewedCount++;
        }
        
        // Log bulk activity
        ActivityLogger::log('Bulk Membership Renewal', 'payments', null, null, [
            'renewed_count' => $renewedCount,
            'plan_id' => $planId,
            'payment_method' => $paymentMethod
        ]);
        
        $db->commit();
        return ['success' => true, 'message' => "Successfully renewed {$renewedCount} memberships."];
        
    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'message' => 'Failed to process bulk renewal: ' . $e->getMessage()];
    }
}

// Get renewal data - only show members expiring within 5 days or already expired
$daysAhead = 5; // Fixed to 5 days as per requirement
$statusFilter = $_GET['status'] ?? 'all';

// Get members needing renewal (within 5 days or expired)
$whereConditions = [
    "m.status IN ('active', 'expired')",
    "m.end_date <= DATE_ADD(CURDATE(), INTERVAL 5 DAY)" // Within 5 days or expired
];
$params = [];

if ($statusFilter === 'expiring') {
    $whereConditions[] = "m.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 5 DAY)";
} elseif ($statusFilter === 'expired') {
    $whereConditions[] = "m.end_date < CURDATE()";
}

$whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

$renewalMembers = $db->fetchAll("
    SELECT m.*,
           p.name as plan_name,
           p.price as plan_price,
           DATEDIFF(m.end_date, CURDATE()) as days_until_expiry,
           CASE
               WHEN m.end_date < CURDATE() THEN 'expired'
               WHEN m.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 2 DAY) THEN 'expiring_soon'
               WHEN m.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 5 DAY) THEN 'expiring'
               ELSE 'active'
           END as renewal_status
    FROM members m
    LEFT JOIN plans p ON m.plan_id = p.id
    $whereClause
    ORDER BY m.end_date ASC
", $params);

// Get plans for renewal options
$plans = $db->fetchAll("SELECT id, name, price, duration_months FROM plans WHERE is_active = 1 ORDER BY name");

$pageTitle = 'Membership Renewals';
include '../includes/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                <i class="fas fa-sync-alt mr-2 text-blue-600"></i>
                Membership Renewals
            </h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Members expiring within 5 days or already expired</p>
        </div>

        <div class="mt-4 sm:mt-0 flex space-x-3">
            <button onclick="toggleBulkRenewal()"
                    class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition duration-200">
                <i class="fas fa-users mr-2"></i>
                Bulk Renewal
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <?php
        $expiredCount = count(array_filter($renewalMembers, fn($m) => $m['renewal_status'] === 'expired'));
        $expiringSoonCount = count(array_filter($renewalMembers, fn($m) => $m['renewal_status'] === 'expiring_soon'));
        $expiringCount = count(array_filter($renewalMembers, fn($m) => $m['renewal_status'] === 'expiring'));
        $totalNeedingRenewal = count($renewalMembers);
        ?>

        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Expired</p>
                    <p class="text-2xl font-bold text-red-600 dark:text-red-400"><?= $expiredCount ?></p>
                </div>
                <div class="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Expiring Soon (2 days)</p>
                    <p class="text-2xl font-bold text-orange-600 dark:text-orange-400"><?= $expiringSoonCount ?></p>
                </div>
                <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-clock text-orange-600 dark:text-orange-400"></i>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Expiring (3-5 days)</p>
                    <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400"><?= $expiringCount ?></p>
                </div>
                <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-calendar-alt text-yellow-600 dark:text-yellow-400"></i>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Needing Renewal</p>
                    <p class="text-2xl font-bold text-blue-600 dark:text-blue-400"><?= $totalNeedingRenewal ?></p>
                </div>
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-sync-alt text-blue-600 dark:text-blue-400"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status Filter</label>
                <select name="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option value="all" <?= $statusFilter === 'all' ? 'selected' : '' ?>>All Members (within 5 days)</option>
                    <option value="expired" <?= $statusFilter === 'expired' ? 'selected' : '' ?>>Expired Only</option>
                    <option value="expiring" <?= $statusFilter === 'expiring' ? 'selected' : '' ?>>Expiring Only (1-5 days)</option>
                </select>
            </div>

            <div class="flex items-end">
                <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-filter mr-2"></i>Apply Filters
                </button>
            </div>
        </form>
    </div>

    <!-- Bulk Renewal Form (Hidden by default) -->
    <div id="bulkRenewalForm" class="hidden bg-blue-50 dark:bg-blue-900/20 rounded-2xl p-6 border border-blue-200 dark:border-blue-800">
        <form method="POST" action="renewals.php?action=bulk_renew">
            <?= Auth::csrfField() ?>
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100">
                    <i class="fas fa-users mr-2"></i>Bulk Renewal
                </h3>
                <button type="button" onclick="toggleBulkRenewal()" class="text-blue-600 hover:text-blue-800 dark:text-blue-400">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">Renewal Plan</label>
                    <select name="bulk_plan_id" required class="w-full px-3 py-2 border border-blue-300 dark:border-blue-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="">Select Plan</option>
                        <?php foreach ($plans as $plan): ?>
                            <option value="<?= $plan['id'] ?>" data-price="<?= $plan['price'] ?>">
                                <?= htmlspecialchars($plan['name']) ?> - <?= formatCurrency($plan['price']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">Payment Method</label>
                    <select name="bulk_payment_method" class="w-full px-3 py-2 border border-blue-300 dark:border-blue-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="cash">Cash</option>
                        <option value="card">Credit/Debit Card</option>
                        <option value="bank_transfer">Bank Transfer</option>
                        <option value="other">Other</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">Payment Date</label>
                    <input type="date" name="bulk_payment_date" value="<?= date('Y-m-d') ?>"
                           class="w-full px-3 py-2 border border-blue-300 dark:border-blue-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                </div>
            </div>

            <div class="flex items-center justify-between">
                <p class="text-sm text-blue-700 dark:text-blue-300">
                    <span id="selectedCount">0</span> members selected for bulk renewal
                </p>
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-sync-alt mr-2"></i>Process Bulk Renewal
                </button>
            </div>
        </form>
    </div>

    <!-- Members Table -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    Members Needing Renewal (<?= count($renewalMembers) ?>)
                </h3>
                <div class="flex items-center space-x-2">
                    <button onclick="selectAll()" class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400">
                        Select All
                    </button>
                    <span class="text-gray-300">|</span>
                    <button onclick="selectNone()" class="text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400">
                        Select None
                    </button>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-6 py-3 text-left">
                            <input type="checkbox" id="selectAllCheckbox" onchange="toggleAll(this)"
                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Member</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Current Plan</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Expires</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Days Left</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    <?php foreach ($renewalMembers as $member): ?>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-6 py-4">
                            <input type="checkbox" name="member_ids[]" value="<?= $member['id'] ?>"
                                   class="member-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                   onchange="updateSelectedCount()">
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div>
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                        <?= htmlspecialchars($member['first_name'] . ' ' . $member['last_name']) ?>
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        ID: <?= htmlspecialchars($member['member_id']) ?>
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900 dark:text-white">
                                <?= htmlspecialchars($member['plan_name'] ?? 'No plan') ?>
                            </div>
                            <?php if ($member['plan_price']): ?>
                                <div class="text-sm text-gray-500 dark:text-gray-400">
                                    <?= formatCurrency($member['plan_price']) ?>
                                </div>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                            <?= $member['end_date'] ? formatDate($member['end_date']) : 'N/A' ?>
                        </td>
                        <td class="px-6 py-4">
                            <?php
                            $statusColors = [
                                'expired' => 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
                                'expiring_soon' => 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
                                'expiring' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
                                'active' => 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            ];
                            $statusColor = $statusColors[$member['renewal_status']] ?? $statusColors['active'];
                            $statusText = [
                                'expired' => 'Expired',
                                'expiring_soon' => 'Expiring Soon',
                                'expiring' => 'Expiring',
                                'active' => 'Active'
                            ];
                            ?>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?= $statusColor ?>">
                                <?= $statusText[$member['renewal_status']] ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                            <?php if ($member['days_until_expiry'] < 0): ?>
                                <span class="text-red-600 font-medium"><?= abs($member['days_until_expiry']) ?> days ago</span>
                            <?php elseif ($member['days_until_expiry'] == 0): ?>
                                <span class="text-orange-600 font-medium">Today</span>
                            <?php else: ?>
                                <?= $member['days_until_expiry'] ?> days
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4 text-right text-sm font-medium">
                            <button onclick="autoRenew(<?= $member['id'] ?>, <?= $member['plan_id'] ?? 'null' ?>, <?= $member['plan_price'] ?? 0 ?>, '<?= htmlspecialchars($member['first_name'] . ' ' . $member['last_name']) ?>')"
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs transition duration-200"
                                    id="renew-<?= $member['id'] ?>">
                                <i class="fas fa-sync-alt mr-1"></i>Renew
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>

                    <?php if (empty($renewalMembers)): ?>
                    <tr>
                        <td colspan="7" class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                            <i class="fas fa-check-circle text-4xl mb-4 text-green-500"></i>
                            <p class="text-lg font-medium">No members need renewal</p>
                            <p class="mt-1">All memberships are up to date!</p>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Renewal Modal -->
<div id="renewalModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 lg:w-1/3 shadow-lg rounded-2xl bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    <i class="fas fa-sync-alt mr-2 text-blue-600"></i>
                    Renew Membership
                </h3>
                <button onclick="closeRenewalModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <form method="POST" action="renewals.php?action=renew" id="renewalForm">
                <?= Auth::csrfField() ?>
                <input type="hidden" name="member_id" id="renewal_member_id">

                <div class="mb-4">
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Member:</p>
                    <p class="font-medium text-gray-900 dark:text-white" id="renewal_member_name"></p>
                </div>

                <div class="space-y-4">
                    <div>
                        <label for="renewal_plan_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Renewal Plan <span class="text-red-500">*</span>
                        </label>
                        <select id="renewal_plan_id" name="plan_id" required onchange="updateRenewalAmount()"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <option value="">Select Plan</option>
                            <?php foreach ($plans as $plan): ?>
                                <option value="<?= $plan['id'] ?>" data-price="<?= $plan['price'] ?>" data-duration="<?= $plan['duration_months'] ?>">
                                    <?= htmlspecialchars($plan['name']) ?> - <?= formatCurrency($plan['price']) ?> (<?= $plan['duration_months'] ?> months)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div>
                        <label for="renewal_amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Amount <span class="text-red-500">*</span>
                        </label>
                        <input type="number" id="renewal_amount" name="amount" step="0.01" min="0" required
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="renewal_payment_method" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Payment Method <span class="text-red-500">*</span>
                            </label>
                            <select id="renewal_payment_method" name="payment_method" required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                <option value="cash">Cash</option>
                                <option value="card">Credit/Debit Card</option>
                                <option value="bank_transfer">Bank Transfer</option>
                                <option value="check">Check</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div>
                            <label for="renewal_payment_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Payment Date <span class="text-red-500">*</span>
                            </label>
                            <input type="date" id="renewal_payment_date" name="payment_date" value="<?= date('Y-m-d') ?>" required
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        </div>
                    </div>

                    <div>
                        <label for="renewal_transaction_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Transaction ID / Reference (Optional)
                        </label>
                        <input type="text" id="renewal_transaction_id" name="transaction_id"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                               placeholder="Enter transaction ID or reference">
                    </div>

                    <div>
                        <label for="renewal_notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Notes (Optional)
                        </label>
                        <textarea id="renewal_notes" name="notes" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                                  placeholder="Any additional notes..."></textarea>
                    </div>
                </div>

                <div class="flex items-center justify-end space-x-4 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <button type="button" onclick="closeRenewalModal()"
                            class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition duration-200">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                        <i class="fas fa-sync-alt mr-2"></i>
                        Process Renewal
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Auto Renewal Function
function autoRenew(memberId, planId, amount, memberName) {
    if (!planId || !amount) {
        alert('Member plan information is missing. Cannot process renewal.');
        return;
    }

    // Use enhanced confirmation modal
    showConfirmModal({
        title: 'Renew Membership',
        message: `
            <div class="space-y-3">
                <p>Are you sure you want to renew membership for <strong>${memberName}</strong>?</p>
                <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg text-sm">
                    <p><strong>Plan:</strong> Current Plan</p>
                    <p><strong>Amount:</strong> $${amount}</p>
                    <p><strong>Payment Method:</strong> Cash</p>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400">This will automatically create a payment record and extend the membership.</p>
            </div>
        `,
        confirmText: 'Renew Membership',
        cancelText: 'Cancel',
        type: 'info',
        onConfirm: () => {
            processRenewal(memberId, planId, amount);
        }
    });

    function processRenewal(memberId, planId, amount) {
        const button = document.getElementById(`renew-${memberId}`);
        const originalText = button.innerHTML;

        // Show loading state
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Processing...';
        button.disabled = true;

        // Prepare form data
        const formData = new FormData();
        formData.append('member_id', memberId);
        formData.append('plan_id', planId);
        formData.append('amount', amount);
        formData.append('csrf_token', '<?= Auth::generateCsrfToken() ?>');

        // Make AJAX request
        fetch('renewals.php?action=quick_renew', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show enhanced success notification
                showToast(data.message, 'success');

                // Remove the member row from the table with animation
                const memberRow = button.closest('tr');
                memberRow.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
                memberRow.style.opacity = '0';
                memberRow.style.transform = 'translateX(100%)';

                setTimeout(() => {
                    memberRow.remove();

                    // Check if table is empty and show message
                    const tbody = document.querySelector('table tbody');
                    const remainingRows = tbody.querySelectorAll('tr').length;

                    if (remainingRows === 0) {
                        tbody.innerHTML = `
                            <tr>
                                <td colspan="7" class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
                                    <i class="fas fa-check-circle text-4xl mb-4 text-green-500"></i>
                                    <p class="text-lg font-medium">No members need renewal</p>
                                    <p class="mt-1">All memberships are up to date!</p>
                                </td>
                            </tr>
                        `;
                    }
                }, 300);
            } else {
                showToast(data.message, 'error');
                // Restore button
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred while processing the renewal.', 'error');
            // Restore button
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}

// Renewal Modal Functions
function openRenewalModal(memberId, memberName, currentPlanId, currentPrice) {
    document.getElementById('renewal_member_id').value = memberId;
    document.getElementById('renewal_member_name').textContent = memberName;

    // Pre-select current plan if available
    if (currentPlanId) {
        document.getElementById('renewal_plan_id').value = currentPlanId;
        updateRenewalAmount();
    }

    document.getElementById('renewalModal').classList.remove('hidden');
}

function closeRenewalModal() {
    document.getElementById('renewalModal').classList.add('hidden');
    document.getElementById('renewalForm').reset();
}

function updateRenewalAmount() {
    const planSelect = document.getElementById('renewal_plan_id');
    const amountInput = document.getElementById('renewal_amount');

    const selectedOption = planSelect.options[planSelect.selectedIndex];
    if (selectedOption && selectedOption.dataset.price) {
        amountInput.value = selectedOption.dataset.price;
    } else {
        amountInput.value = '';
    }
}

// Bulk Renewal Functions
function toggleBulkRenewal() {
    const form = document.getElementById('bulkRenewalForm');
    form.classList.toggle('hidden');

    if (!form.classList.contains('hidden')) {
        updateSelectedCount();
    }
}

function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.member-checkbox:checked');
    const count = checkboxes.length;
    document.getElementById('selectedCount').textContent = count;

    // Update bulk renewal form with selected member IDs
    const bulkForm = document.querySelector('#bulkRenewalForm form');

    // Remove existing hidden inputs
    bulkForm.querySelectorAll('input[name="member_ids[]"]').forEach(input => input.remove());

    // Add new hidden inputs for selected members
    checkboxes.forEach(checkbox => {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'member_ids[]';
        hiddenInput.value = checkbox.value;
        bulkForm.appendChild(hiddenInput);
    });
}

function toggleAll(masterCheckbox) {
    const checkboxes = document.querySelectorAll('.member-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = masterCheckbox.checked;
    });
    updateSelectedCount();
}

function selectAll() {
    const checkboxes = document.querySelectorAll('.member-checkbox');
    const masterCheckbox = document.getElementById('selectAllCheckbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    masterCheckbox.checked = true;
    updateSelectedCount();
}

function selectNone() {
    const checkboxes = document.querySelectorAll('.member-checkbox');
    const masterCheckbox = document.getElementById('selectAllCheckbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    masterCheckbox.checked = false;
    updateSelectedCount();
}

// Close modal when clicking outside
document.getElementById('renewalModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeRenewalModal();
    }
});

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateSelectedCount();
});
</script>

<?php include '../includes/footer.php'; ?>
