<?php
/**
 * Print Receipt API
 * MyGym Management System
 */

session_start();

// Check authentication (simple check)
if (!isset($_SESSION['user_id']) && !isset($_SESSION['local_admin'])) {
    http_response_code(401);
    exit('Unauthorized');
}

// Get payment ID
$paymentId = $_GET['id'] ?? null;
if (!$paymentId || !is_numeric($paymentId)) {
    http_response_code(400);
    exit('Invalid payment ID');
}

try {
    // Include database config
    $config = require_once '../config/config.php';

    // Create PDO connection
    $pdo = new PDO(
        "mysql:host={$config['database']['host']};dbname={$config['database']['name']};charset=utf8mb4",
        $config['database']['username'],
        $config['database']['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Get payment
    $stmt = $pdo->prepare("SELECT * FROM payments WHERE id = ?");
    $stmt->execute([$paymentId]);
    $payment = $stmt->fetch();

    if (!$payment) {
        http_response_code(404);
        exit('Payment not found');
    }

    // Get member
    $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
    $stmt->execute([$payment['member_id']]);
    $member = $stmt->fetch();

    if (!$member) {
        http_response_code(404);
        exit('Member not found');
    }

    // Get plan
    $stmt = $pdo->prepare("SELECT * FROM plans WHERE id = ?");
    $stmt->execute([$payment['plan_id']]);
    $plan = $stmt->fetch();

    // Get user (if exists)
    $processedByName = 'Local Admin';
    if ($payment['processed_by']) {
        $stmt = $pdo->prepare("SELECT name FROM users WHERE id = ?");
        $stmt->execute([$payment['processed_by']]);
        $user = $stmt->fetch();
        if ($user) {
            $processedByName = $user['name'];
        }
    }

    // Get gym settings
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM config WHERE setting_key IN ('gym_name', 'gym_address', 'gym_phone', 'gym_email', 'currency')");
    $stmt->execute();
    $settings = $stmt->fetchAll();

    $gymSettings = [];
    foreach ($settings as $setting) {
        $gymSettings[$setting['setting_key']] = $setting['setting_value'];
    }

    $gymName = $gymSettings['gym_name'] ?? 'MyGym';
    $gymAddress = $gymSettings['gym_address'] ?? '';
    $gymPhone = $gymSettings['gym_phone'] ?? '';
    $gymEmail = $gymSettings['gym_email'] ?? '';
    $currency = $gymSettings['currency'] ?? 'USD';
    
    // Format currency
    function formatCurrency($amount, $currency = 'USD') {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'INR' => '₹',
            'JPY' => '¥'
        ];
        
        $symbol = $symbols[$currency] ?? $currency . ' ';
        return $symbol . number_format($amount, 2);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    exit('Error retrieving payment details');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt - <?= htmlspecialchars($payment['receipt_number']) ?></title>
    <style>
        /* Thermal printer optimized styles */
        @media print {
            @page {
                size: 80mm auto;
                margin: 0;
            }
            
            body {
                margin: 0;
                padding: 5mm;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                line-height: 1.2;
                color: #000;
                background: #fff;
            }
            
            .no-print {
                display: none !important;
            }
        }
        
        /* Screen styles */
        body {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
            max-width: 300px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .receipt {
            background: white;
            padding: 20px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        
        .gym-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .gym-info {
            font-size: 12px;
            margin-bottom: 2px;
        }
        
        .receipt-title {
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            margin: 15px 0;
            text-decoration: underline;
        }
        
        .receipt-number {
            text-align: center;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .section {
            margin-bottom: 15px;
        }
        
        .row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
        }
        
        .label {
            font-weight: bold;
        }
        
        .divider {
            border-top: 1px dashed #000;
            margin: 10px 0;
        }
        
        .total {
            border-top: 2px solid #000;
            border-bottom: 2px solid #000;
            padding: 8px 0;
            font-weight: bold;
            font-size: 16px;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            border-top: 1px solid #000;
            padding-top: 10px;
        }
        
        .print-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        
        .print-button:hover {
            background: #0056b3;
        }
        
        .button-container {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="button-container no-print">
        <button class="print-button" onclick="window.print()">🖨️ Print Receipt</button>
        <button class="print-button" onclick="window.close()">❌ Close</button>
    </div>
    
    <div class="receipt">
        <!-- Header -->
        <div class="header">
            <div class="gym-name"><?= htmlspecialchars($gymName) ?></div>
            <?php if ($gymAddress): ?>
                <div class="gym-info"><?= htmlspecialchars($gymAddress) ?></div>
            <?php endif; ?>
            <?php if ($gymPhone): ?>
                <div class="gym-info">Tel: <?= htmlspecialchars($gymPhone) ?></div>
            <?php endif; ?>
            <?php if ($gymEmail): ?>
                <div class="gym-info"><?= htmlspecialchars($gymEmail) ?></div>
            <?php endif; ?>
        </div>
        
        <!-- Receipt Title -->
        <div class="receipt-title">PAYMENT RECEIPT</div>
        
        <!-- Receipt Number -->
        <div class="receipt-number">
            Receipt #: <?= htmlspecialchars($payment['receipt_number']) ?>
        </div>
        
        <!-- Member Information -->
        <div class="section">
            <div class="row">
                <span class="label">Member:</span>
                <span><?= htmlspecialchars($member['first_name'] . ' ' . $member['last_name']) ?></span>
            </div>
            <div class="row">
                <span class="label">Member ID:</span>
                <span><?= htmlspecialchars($member['member_id']) ?></span>
            </div>
            <?php if ($member['phone']): ?>
            <div class="row">
                <span class="label">Phone:</span>
                <span><?= htmlspecialchars($member['phone']) ?></span>
            </div>
            <?php endif; ?>
        </div>
        
        <div class="divider"></div>
        
        <!-- Payment Details -->
        <div class="section">
            <div class="row">
                <span class="label">Plan:</span>
                <span><?= htmlspecialchars($plan ? $plan['name'] : 'Unknown Plan') ?></span>
            </div>
            <div class="row">
                <span class="label">Duration:</span>
                <span><?= $plan ? $plan['duration_months'] : 0 ?> month(s)</span>
            </div>
            <div class="row">
                <span class="label">Start Date:</span>
                <span><?= date('d/m/Y', strtotime($payment['start_date'])) ?></span>
            </div>
            <div class="row">
                <span class="label">End Date:</span>
                <span><?= date('d/m/Y', strtotime($payment['end_date'])) ?></span>
            </div>
        </div>
        
        <div class="divider"></div>
        
        <!-- Payment Information -->
        <div class="section">
            <div class="row">
                <span class="label">Payment Date:</span>
                <span><?= date('d/m/Y', strtotime($payment['payment_date'])) ?></span>
            </div>
            <div class="row">
                <span class="label">Payment Method:</span>
                <span><?= ucfirst(str_replace('_', ' ', $payment['payment_method'])) ?></span>
            </div>
            <?php if ($payment['transaction_id']): ?>
            <div class="row">
                <span class="label">Transaction ID:</span>
                <span><?= htmlspecialchars($payment['transaction_id']) ?></span>
            </div>
            <?php endif; ?>
            <div class="row">
                <span class="label">Processed By:</span>
                <span><?= htmlspecialchars($processedByName) ?></span>
            </div>
        </div>
        
        <!-- Total Amount -->
        <div class="total">
            <div class="row">
                <span class="label">TOTAL AMOUNT:</span>
                <span><?= formatCurrency($payment['amount'], $currency) ?></span>
            </div>
        </div>
        
        <?php if ($payment['notes'] && !strpos($payment['notes'], 'Local Admin')): ?>
        <div class="divider"></div>
        <div class="section">
            <div class="label">Notes:</div>
            <div><?= htmlspecialchars(str_replace(' (Local Admin)', '', $payment['notes'])) ?></div>
        </div>
        <?php endif; ?>
        
        <!-- Footer -->
        <div class="footer">
            <div>Thank you for your membership!</div>
            <div style="margin-top: 5px;">
                Printed on: <?= date('d/m/Y H:i') ?>
            </div>
        </div>
    </div>
    
    <script>
        // Auto-print when page loads (for thermal printers)
        window.addEventListener('load', function() {
            // Small delay to ensure content is fully rendered
            setTimeout(function() {
                if (window.location.search.includes('auto_print=1')) {
                    window.print();
                }
            }, 500);
        });
    </script>
</body>
</html>
