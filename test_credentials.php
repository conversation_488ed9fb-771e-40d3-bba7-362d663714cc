<?php
/**
 * Test Specific Credentials
 * MyGym Management System
 */

session_start();

echo "<h2>🔍 Testing Credentials: <EMAIL></h2>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .info { color: blue; } .warning { color: orange; }</style>";

// Check if system is installed
if (!file_exists('config/config.php')) {
    echo '<p class="error">❌ System not installed. Please run install.php first.</p>';
    echo '<a href="install.php" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🚀 Run Installation Wizard</a>';
    exit;
}

try {
    // Load database
    require_once 'config/database.php';
    $db = Database::getInstance();
    echo "<p class='success'>✅ Database connection successful</p>";
    
    // Test the exact credentials you provided
    $testEmail = '<EMAIL>';
    $testPassword = 'admin123456';
    
    echo "<h3>🔐 Testing Login for: $testEmail</h3>";
    
    // Check if user exists
    $user = $db->fetch("SELECT id, name, email, password, role, is_active FROM users WHERE email = ?", [$testEmail]);
    
    if (!$user) {
        echo "<p class='error'>❌ User NOT found with email: $testEmail</p>";
        
        // Show all users in database
        $allUsers = $db->fetchAll("SELECT id, name, email, role, is_active FROM users");
        echo "<h4>📋 All users in database:</h4>";
        if (empty($allUsers)) {
            echo "<p class='warning'>⚠️ No users found in database! Installation may have failed.</p>";
            echo "<p><a href='install.php'>🔄 Re-run Installation</a></p>";
        } else {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Role</th><th>Active</th></tr>";
            foreach ($allUsers as $u) {
                echo "<tr>";
                echo "<td>" . $u['id'] . "</td>";
                echo "<td>" . htmlspecialchars($u['name']) . "</td>";
                echo "<td>" . htmlspecialchars($u['email']) . "</td>";
                echo "<td>" . htmlspecialchars($u['role']) . "</td>";
                echo "<td>" . ($u['is_active'] ? 'Yes' : 'No') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "<p class='info'>💡 Use one of the emails above to login</p>";
        }
    } else {
        echo "<p class='success'>✅ User found!</p>";
        echo "<p class='info'>👤 Name: " . htmlspecialchars($user['name']) . "</p>";
        echo "<p class='info'>📧 Email: " . htmlspecialchars($user['email']) . "</p>";
        echo "<p class='info'>🔑 Role: " . htmlspecialchars($user['role']) . "</p>";
        echo "<p class='info'>🟢 Active: " . ($user['is_active'] ? 'Yes' : 'No') . "</p>";
        
        // Test password
        echo "<h4>🔒 Testing password: admin123456</h4>";
        
        if (password_verify($testPassword, $user['password'])) {
            echo "<p class='success'>✅ Password is CORRECT!</p>";
            echo "<p class='success'>🎉 Your credentials should work. There might be an issue with the login form.</p>";
            
            // Test the actual login process
            echo "<h4>🧪 Testing actual login process...</h4>";

            // Check if Auth class exists and has login method
            if (file_exists('includes/auth.php')) {
                echo "<p class='info'>✅ Auth file exists</p>";
                // We'll skip the Auth::login test for now since it might not be implemented
                echo "<p class='info'>📝 Auth class test skipped - will test manual login logic</p>";
            } else {
                echo "<p class='warning'>⚠️ Auth file missing - this might be the issue!</p>";
            }
            
        } else {
            echo "<p class='error'>❌ Password is INCORRECT!</p>";
            echo "<p class='warning'>🔍 Stored password hash: " . substr($user['password'], 0, 30) . "...</p>";
            echo "<p class='warning'>🔍 Hash length: " . strlen($user['password']) . " characters</p>";
            
            // Check if it's a bcrypt hash
            if (substr($user['password'], 0, 4) === '$2y$') {
                echo "<p class='info'>✅ Password is properly hashed with bcrypt</p>";
                echo "<p class='error'>❌ But your password 'admin123456' doesn't match</p>";
                
                // Offer to reset password
                echo "<h4>🔧 Fix Options:</h4>";
                echo "<p><strong>Option 1:</strong> Reset password to 'admin123456'</p>";
                echo "<a href='?reset_password=yes' style='background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Reset Password</a>";
                
            } else {
                echo "<p class='error'>❌ Password is not properly hashed!</p>";
                echo "<p class='warning'>This is a security issue. Password should be bcrypt hashed.</p>";
            }
        }
    }
    
    // Handle password reset
    if (isset($_GET['reset_password']) && $_GET['reset_password'] === 'yes') {
        echo "<h4>🔄 Resetting password...</h4>";
        
        $newPasswordHash = password_hash('admin123456', PASSWORD_DEFAULT);
        $updated = $db->update('users', ['password' => $newPasswordHash], 'email = ?', [$testEmail]);
        
        if ($updated) {
            echo "<p class='success'>✅ Password reset successfully!</p>";
            echo "<p class='success'>You can now login with:</p>";
            echo "<p><strong>Email:</strong> <EMAIL></p>";
            echo "<p><strong>Password:</strong> admin123456</p>";
            echo "<a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Try Login Now</a>";
        } else {
            echo "<p class='error'>❌ Failed to reset password</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p class='info'>Full error details:</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<h3>🔧 Quick Actions:</h3>";
echo "<a href='index.php' style='background: #007cba; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;'>🔑 Try Login</a>";
echo "<a href='install.php' style='background: #6c757d; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;'>🔄 Re-install</a>";
echo "<a href='reset_system.php' style='background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin: 5px;'>🗑️ Reset System</a>";
?>
