<!-- Step 2: Site Settings -->
<div class="text-center mb-6">
    <i class="fas fa-cog text-4xl text-blue-600 mb-4"></i>
    <h2 class="text-2xl font-bold text-gray-900 mb-2">Site Configuration</h2>
    <p class="text-gray-600">Configure your gym's basic information</p>
</div>

<form method="POST" class="space-y-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="md:col-span-2">
            <label for="gym_name" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-dumbbell mr-2"></i>Gym Name
            </label>
            <input type="text" 
                   id="gym_name" 
                   name="gym_name" 
                   value="<?= htmlspecialchars($_POST['gym_name'] ?? 'MyGym Fitness Center') ?>"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                   placeholder="MyGym Fitness Center"
                   required>
            <p class="mt-1 text-sm text-gray-500">This will appear in the header and emails</p>
        </div>
        
        <div class="md:col-span-2">
            <label for="site_url" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-globe mr-2"></i>Site URL
            </label>
            <input type="url" 
                   id="site_url" 
                   name="site_url" 
                   value="<?= htmlspecialchars($_POST['site_url'] ?? 'http://localhost/mygym') ?>"
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                   placeholder="https://yourgym.com"
                   required>
            <p class="mt-1 text-sm text-gray-500">Full URL where your gym system will be accessible</p>
        </div>
        
        <div>
            <label for="currency" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-dollar-sign mr-2"></i>Currency
            </label>
            <select id="currency" 
                    name="currency" 
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="USD" <?= ($_POST['currency'] ?? 'USD') === 'USD' ? 'selected' : '' ?>>USD - US Dollar</option>
                <option value="EUR" <?= ($_POST['currency'] ?? '') === 'EUR' ? 'selected' : '' ?>>EUR - Euro</option>
                <option value="GBP" <?= ($_POST['currency'] ?? '') === 'GBP' ? 'selected' : '' ?>>GBP - British Pound</option>
                <option value="CAD" <?= ($_POST['currency'] ?? '') === 'CAD' ? 'selected' : '' ?>>CAD - Canadian Dollar</option>
                <option value="AUD" <?= ($_POST['currency'] ?? '') === 'AUD' ? 'selected' : '' ?>>AUD - Australian Dollar</option>
                <option value="INR" <?= ($_POST['currency'] ?? '') === 'INR' ? 'selected' : '' ?>>INR - Indian Rupee</option>
                <option value="JPY" <?= ($_POST['currency'] ?? '') === 'JPY' ? 'selected' : '' ?>>JPY - Japanese Yen</option>
            </select>
        </div>
        
        <div>
            <label for="language" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-language mr-2"></i>Default Language
            </label>
            <select id="language" 
                    name="language" 
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="en" <?= ($_POST['language'] ?? 'en') === 'en' ? 'selected' : '' ?>>English</option>
                <option value="fr" <?= ($_POST['language'] ?? '') === 'fr' ? 'selected' : '' ?>>Français</option>
                <option value="es" <?= ($_POST['language'] ?? '') === 'es' ? 'selected' : '' ?>>Español</option>
                <option value="de" <?= ($_POST['language'] ?? '') === 'de' ? 'selected' : '' ?>>Deutsch</option>
                <option value="ar" <?= ($_POST['language'] ?? '') === 'ar' ? 'selected' : '' ?>>العربية</option>
            </select>
        </div>
    </div>
    
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex">
            <i class="fas fa-lightbulb text-green-400 mt-1 mr-3"></i>
            <div class="text-green-800">
                <h4 class="font-medium">Pro Tips:</h4>
                <ul class="mt-2 text-sm list-disc list-inside">
                    <li>You can change these settings later in the admin panel</li>
                    <li>Make sure your site URL is correct for email links to work properly</li>
                    <li>Currency affects how prices are displayed throughout the system</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="flex justify-between">
        <a href="install.php?step=1" 
           class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-8 rounded-lg transition duration-200 flex items-center">
            <i class="fas fa-arrow-left mr-2"></i>
            Back
        </a>
        
        <button type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-lg transition duration-200 flex items-center">
            Continue
            <i class="fas fa-arrow-right ml-2"></i>
        </button>
    </div>
</form>
