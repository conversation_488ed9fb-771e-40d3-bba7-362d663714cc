<?php
/**
 * Automatic Installation Script
 * MyGym Management System
 */

session_start();

echo "<h2>🚀 MyGym Auto Installation</h2>";
echo "<style>body { font-family: Arial, sans-serif; margin: 20px; } .success { color: green; } .error { color: red; } .info { color: blue; } .warning { color: orange; }</style>";

// Check if already installed
if (file_exists('config/config.php')) {
    echo '<p class="error">❌ System already installed. Please reset first.</p>';
    echo '<a href="reset_system.php" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🔄 Reset System</a>';
    exit;
}

// Auto installation configuration
$config = [
    'db_host' => 'localhost',
    'db_name' => 'mygym',
    'db_username' => 'root',
    'db_password' => '',
    'gym_name' => 'MyGym Fitness Center',
    'site_url' => 'http://localhost/mygym',
    'currency' => 'USD',
    'language' => 'en',
    'admin_name' => 'Admin User',
    'admin_email' => '<EMAIL>',
    'admin_password' => 'admin123456'
];

echo "<h3>📋 Installation Configuration:</h3>";
echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
foreach ($config as $key => $value) {
    $displayValue = ($key === 'admin_password') ? str_repeat('*', strlen($value)) : $value;
    echo "<tr><td>" . ucfirst(str_replace('_', ' ', $key)) . "</td><td>" . htmlspecialchars($displayValue) . "</td></tr>";
}
echo "</table>";

echo "<h3>🔧 Starting Installation...</h3>";

try {
    // Step 1: Database Setup
    echo "<p class='info'>Step 1: Testing database connection...</p>";
    
    $dsn = "mysql:host={$config['db_host']};charset=utf8mb4";
    $pdo = new PDO($dsn, $config['db_username'], $config['db_password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p class='success'>✅ Database connection successful</p>";
    
    // Create database if not exists
    $stmt = $pdo->prepare("CREATE DATABASE IF NOT EXISTS `{$config['db_name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $stmt->execute();
    echo "<p class='success'>✅ Database '{$config['db_name']}' created/verified</p>";
    
    // Step 2: Create config file
    echo "<p class='info'>Step 2: Creating configuration file...</p>";
    
    $configContent = "<?php\nreturn [\n";
    $configContent .= "    'database' => [\n";
    $configContent .= "        'host' => '{$config['db_host']}',\n";
    $configContent .= "        'name' => '{$config['db_name']}',\n";
    $configContent .= "        'username' => '{$config['db_username']}',\n";
    $configContent .= "        'password' => '{$config['db_password']}'\n";
    $configContent .= "    ],\n";
    $configContent .= "    'app' => [\n";
    $configContent .= "        'name' => '{$config['gym_name']}',\n";
    $configContent .= "        'url' => '{$config['site_url']}',\n";
    $configContent .= "        'currency' => '{$config['currency']}',\n";
    $configContent .= "        'language' => '{$config['language']}'\n";
    $configContent .= "    ]\n";
    $configContent .= "];\n";
    
    if (!is_dir('config')) {
        mkdir('config', 0755, true);
    }
    
    file_put_contents('config/config.php', $configContent);
    echo "<p class='success'>✅ Configuration file created</p>";
    
    // Step 3: Install database schema
    echo "<p class='info'>Step 3: Installing database schema...</p>";
    
    $dsn = "mysql:host={$config['db_host']};dbname={$config['db_name']};charset=utf8mb4";
    $pdo = new PDO($dsn, $config['db_username'], $config['db_password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Execute schema
    $schema = file_get_contents('schema.sql');
    $pdo->exec($schema);
    echo "<p class='success'>✅ Database tables created</p>";
    
    // Step 4: Create admin user
    echo "<p class='info'>Step 4: Creating admin user...</p>";
    
    $hashedPassword = password_hash($config['admin_password'], PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, is_active) VALUES (?, ?, ?, 'admin', 1)");
    $stmt->execute([$config['admin_name'], $config['admin_email'], $hashedPassword]);
    echo "<p class='success'>✅ Admin user created</p>";
    
    // Step 5: Insert site settings
    echo "<p class='info'>Step 5: Configuring site settings...</p>";
    
    $settings = [
        'gym_name' => $config['gym_name'],
        'site_url' => $config['site_url'],
        'currency' => $config['currency'],
        'language' => $config['language'],
        'timezone' => 'UTC',
        'date_format' => 'Y-m-d',
        'time_format' => 'H:i'
    ];
    
    foreach ($settings as $key => $value) {
        $stmt = $pdo->prepare("INSERT INTO config (setting_key, setting_value) VALUES (?, ?)");
        $stmt->execute([$key, $value]);
    }
    echo "<p class='success'>✅ Site settings configured</p>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 20px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>🎉 Installation Complete!</h3>";
    echo "<p style='color: #155724;'>Your MyGym Management System has been successfully installed.</p>";
    echo "</div>";
    
    echo "<h3>🔑 Login Credentials:</h3>";
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0;'>";
    echo "<p><strong>Email:</strong> " . htmlspecialchars($config['admin_email']) . "</p>";
    echo "<p><strong>Password:</strong> " . htmlspecialchars($config['admin_password']) . "</p>";
    echo "</div>";
    
    echo "<h3>🚀 Quick Actions:</h3>";
    echo "<a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔑 Login Now</a>";
    echo "<a href='dashboard.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📊 Go to Dashboard</a>";
    echo "<a href='test_credentials.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🧪 Test Login</a>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Installation failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p class='info'>Full error details:</p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    
    echo "<h3>🔧 Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>Make sure XAMPP/MySQL is running</li>";
    echo "<li>Check if database credentials are correct</li>";
    echo "<li>Ensure the 'mygym' database can be created</li>";
    echo "<li>Verify file permissions for config folder</li>";
    echo "</ul>";
    
    echo "<a href='reset_system.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔄 Reset & Try Again</a>";
}
?>
