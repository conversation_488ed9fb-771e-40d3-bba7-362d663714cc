<?php
/**
 * Plans Management Page
 * MyGym Management System
 */

session_start();
require_once '../config/database.php';
require_once '../includes/auth.php';

// Require authentication and permission
Auth::requireAuth();
Auth::requirePermission('plans.view');

$db = Database::getInstance();
$action = $_GET['action'] ?? 'list';
$planId = $_GET['id'] ?? null;
$error = '';
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add' || $action === 'edit') {
        $result = handlePlanSave();
        if ($result['success']) {
            Session::success($result['message']);
            header('Location: plans.php');
            exit;
        } else {
            $error = $result['message'];
        }
    } elseif ($action === 'delete') {
        $result = handlePlanDelete();
        if ($result['success']) {
            Session::success($result['message']);
        } else {
            Session::error($result['message']);
        }
        header('Location: plans.php');
        exit;
    }
}

function handlePlanSave() {
    global $db, $action, $planId;
    
    // Validate input
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $durationMonths = $_POST['duration_months'] ?? null;
    $price = $_POST['price'] ?? null;
    $features = trim($_POST['features'] ?? '');
    
    if (empty($name)) {
        return ['success' => false, 'message' => 'Plan name is required.'];
    }
    
    if (!$durationMonths || !is_numeric($durationMonths) || $durationMonths <= 0) {
        return ['success' => false, 'message' => 'Please enter a valid duration in months.'];
    }
    
    if (!$price || !is_numeric($price) || $price < 0) {
        return ['success' => false, 'message' => 'Please enter a valid price.'];
    }
    
    try {
        $db->beginTransaction();
        
        $data = [
            'name' => $name,
            'description' => $description ?: null,
            'duration_months' => (int)$durationMonths,
            'price' => (float)$price,
            'features' => $features ?: null
        ];
        
        if ($action === 'add') {
            $data['is_active'] = 1;
            $newPlanId = $db->insert('plans', $data);
            
            // Log activity
            ActivityLogger::log('Plan Created', 'plans', $newPlanId, null, $data);
            
            $message = 'Plan added successfully.';
        } else {
            // Update existing plan
            $oldData = $db->fetch("SELECT * FROM plans WHERE id = ?", [$planId]);
            $db->update('plans', $data, 'id = ?', [$planId]);
            
            // Log activity
            ActivityLogger::log('Plan Updated', 'plans', $planId, $oldData, $data);
            
            $message = 'Plan updated successfully.';
        }
        
        $db->commit();
        return ['success' => true, 'message' => $message];
        
    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'message' => 'Failed to save plan: ' . $e->getMessage()];
    }
}

function handlePlanDelete() {
    global $db;
    
    $planId = $_POST['plan_id'] ?? null;
    if (!$planId) {
        return ['success' => false, 'message' => 'Plan ID is required.'];
    }
    
    try {
        $plan = $db->fetch("SELECT * FROM plans WHERE id = ?", [$planId]);
        if (!$plan) {
            return ['success' => false, 'message' => 'Plan not found.'];
        }
        
        // Check if plan has active members
        $activeMembers = $db->fetch("SELECT COUNT(*) as count FROM members WHERE plan_id = ? AND status = 'active'", [$planId])['count'];
        if ($activeMembers > 0) {
            return ['success' => false, 'message' => 'Cannot delete plan with active members. Please reassign members first.'];
        }
        
        $db->beginTransaction();
        
        // Soft delete plan
        $db->update('plans', ['is_active' => 0], 'id = ?', [$planId]);
        
        // Log activity
        ActivityLogger::log('Plan Deleted', 'plans', $planId, $plan);
        
        $db->commit();
        return ['success' => true, 'message' => 'Plan deleted successfully.'];
        
    } catch (Exception $e) {
        $db->rollback();
        return ['success' => false, 'message' => 'Failed to delete plan: ' . $e->getMessage()];
    }
}

// Get plan data for edit
$plan = null;
if ($action === 'edit' && $planId) {
    $plan = $db->fetch("SELECT * FROM plans WHERE id = ?", [$planId]);
    if (!$plan) {
        Session::error('Plan not found.');
        header('Location: plans.php');
        exit;
    }
}

// Get plans list with member counts
$plans = $db->fetchAll("
    SELECT p.*, 
           COUNT(m.id) as active_members,
           COUNT(py.id) as total_payments,
           COALESCE(SUM(py.amount), 0) as total_revenue
    FROM plans p
    LEFT JOIN members m ON p.id = m.plan_id AND m.status = 'active'
    LEFT JOIN payments py ON p.id = py.plan_id
    GROUP BY p.id
    ORDER BY p.created_at DESC
");

$pageTitle = 'Plans';
include '../includes/header.php';
?>

<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Membership Plans</h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Manage your gym membership plans and pricing</p>
        </div>
        
        <?php if (Auth::can('plans.create')): ?>
        <div class="mt-4 sm:mt-0">
            <a href="plans.php?action=add" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                <i class="fas fa-plus mr-2"></i>
                Add Plan
            </a>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Plans</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= count($plans) ?></p>
                </div>
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-tags text-blue-600 dark:text-blue-400"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Plans</p>
                    <?php
                    $activePlans = array_filter($plans, fn($p) => $p['is_active']);
                    ?>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= count($activePlans) ?></p>
                </div>
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Members</p>
                    <?php
                    $totalActiveMembers = array_sum(array_column($plans, 'active_members'));
                    ?>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($totalActiveMembers) ?></p>
                </div>
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-users text-purple-600 dark:text-purple-400"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Revenue</p>
                    <?php
                    $totalRevenue = array_sum(array_column($plans, 'total_revenue'));
                    ?>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white"><?= formatCurrency($totalRevenue) ?></p>
                </div>
                <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-xl flex items-center justify-center">
                    <i class="fas fa-dollar-sign text-orange-600 dark:text-orange-400"></i>
                </div>
            </div>
        </div>
    </div>

    <?php if ($action === 'list'): ?>
    <!-- Plans Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php foreach ($plans as $planRow): ?>
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700 <?= !$planRow['is_active'] ? 'opacity-60' : '' ?>">
            <div class="flex items-start justify-between mb-4">
                <div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white">
                        <?= htmlspecialchars($planRow['name']) ?>
                    </h3>
                    <?php if ($planRow['description']): ?>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                            <?= htmlspecialchars($planRow['description']) ?>
                        </p>
                    <?php endif; ?>
                </div>

                <div class="flex items-center space-x-2">
                    <?php if ($planRow['is_active']): ?>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Active
                        </span>
                    <?php else: ?>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                            Inactive
                        </span>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Price -->
            <div class="text-center mb-6">
                <div class="text-4xl font-bold text-blue-600 dark:text-blue-400">
                    <?= formatCurrency($planRow['price']) ?>
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    for <?= $planRow['duration_months'] ?> month<?= $planRow['duration_months'] > 1 ? 's' : '' ?>
                </div>
                <div class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                    <?= formatCurrency($planRow['price'] / $planRow['duration_months']) ?>/month
                </div>
            </div>

            <!-- Features -->
            <?php if ($planRow['features']): ?>
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Features:</h4>
                    <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                        <?php foreach (explode(',', $planRow['features']) as $feature): ?>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-2 text-xs"></i>
                                <?= htmlspecialchars(trim($feature)) ?>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <!-- Stats -->
            <div class="grid grid-cols-2 gap-4 mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="text-center">
                    <div class="text-lg font-bold text-gray-900 dark:text-white">
                        <?= $planRow['active_members'] ?>
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">Active Members</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-bold text-gray-900 dark:text-white">
                        <?= formatCurrency($planRow['total_revenue']) ?>
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">Total Revenue</div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                <div class="flex items-center space-x-2">
                    <?php if (Auth::can('plans.edit')): ?>
                        <a href="plans.php?action=edit&id=<?= $planRow['id'] ?>"
                           class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                            <i class="fas fa-edit"></i>
                        </a>
                    <?php endif; ?>

                    <a href="members.php?plan=<?= $planRow['id'] ?>"
                       class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                       title="View members with this plan">
                        <i class="fas fa-users"></i>
                    </a>

                    <?php if (Auth::can('plans.delete') && $planRow['active_members'] == 0): ?>
                        <button onclick="deletePlan(<?= $planRow['id'] ?>, '<?= htmlspecialchars($planRow['name']) ?>')"
                                class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                title="Delete Plan">
                            <i class="fas fa-trash"></i>
                        </button>
                    <?php endif; ?>
                </div>

                <span class="text-xs text-gray-500 dark:text-gray-400">
                    Created <?= timeAgo($planRow['created_at']) ?>
                </span>
            </div>
        </div>
        <?php endforeach; ?>

        <?php if (empty($plans)): ?>
        <div class="col-span-full">
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-12 text-center shadow-sm border border-gray-100 dark:border-gray-700">
                <i class="fas fa-tags text-4xl text-gray-400 mb-4"></i>
                <p class="text-lg font-medium text-gray-500 dark:text-gray-400">No plans found</p>
                <p class="mt-1 text-gray-400 dark:text-gray-500">Get started by creating your first membership plan.</p>
                <?php if (Auth::can('plans.create')): ?>
                    <a href="plans.php?action=add"
                       class="inline-flex items-center mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Create First Plan
                    </a>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php if ($action === 'add' || $action === 'edit'): ?>
    <!-- Add/Edit Plan Form -->
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-100 dark:border-gray-700">
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                <?= $action === 'add' ? 'Create New Plan' : 'Edit Plan' ?>
            </h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                <?= $action === 'add' ? 'Define a new membership plan for your gym.' : 'Update the plan information.' ?>
            </p>
        </div>

        <?php if ($error): ?>
            <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex">
                    <i class="fas fa-exclamation-circle text-red-400 mt-0.5 mr-3"></i>
                    <span class="text-red-800"><?= htmlspecialchars($error) ?></span>
                </div>
            </div>
        <?php endif; ?>

        <form method="POST" class="space-y-6">
            <?= Auth::csrfField() ?>

            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Plan Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text"
                           id="name"
                           name="name"
                           value="<?= htmlspecialchars($plan['name'] ?? $_POST['name'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                           placeholder="e.g., Premium Membership"
                           required>
                </div>

                <div>
                    <label for="duration_months" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Duration (Months) <span class="text-red-500">*</span>
                    </label>
                    <input type="number"
                           id="duration_months"
                           name="duration_months"
                           value="<?= htmlspecialchars($plan['duration_months'] ?? $_POST['duration_months'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                           min="1"
                           required>
                </div>

                <div>
                    <label for="price" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Price <span class="text-red-500">*</span>
                    </label>
                    <input type="number"
                           id="price"
                           name="price"
                           value="<?= htmlspecialchars($plan['price'] ?? $_POST['price'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                           step="0.01"
                           min="0"
                           placeholder="0.00"
                           required>
                </div>

                <div class="flex items-end">
                    <div class="w-full">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Monthly Rate</label>
                        <div id="monthly-rate" class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-600 dark:text-gray-400">
                            $0.00/month
                        </div>
                    </div>
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</label>
                <textarea id="description"
                          name="description"
                          rows="3"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                          placeholder="Brief description of the plan..."><?= htmlspecialchars($plan['description'] ?? $_POST['description'] ?? '') ?></textarea>
            </div>

            <!-- Features -->
            <div>
                <label for="features" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Features</label>
                <textarea id="features"
                          name="features"
                          rows="4"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                          placeholder="Enter features separated by commas (e.g., Access to all equipment, Group classes, Locker access)"><?= htmlspecialchars($plan['features'] ?? $_POST['features'] ?? '') ?></textarea>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Separate features with commas. Each feature will be displayed as a bullet point.</p>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <a href="plans.php"
                   class="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition duration-200">
                    Cancel
                </a>
                <button type="submit"
                        class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition duration-200">
                    <i class="fas fa-save mr-2"></i>
                    <?= $action === 'add' ? 'Create Plan' : 'Update Plan' ?>
                </button>
            </div>
        </form>
    </div>

    <script>
    // Calculate monthly rate
    function updateMonthlyRate() {
        const price = parseFloat(document.getElementById('price').value) || 0;
        const duration = parseInt(document.getElementById('duration_months').value) || 1;
        const monthlyRate = price / duration;

        document.getElementById('monthly-rate').textContent =
            new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(monthlyRate) + '/month';
    }

    document.getElementById('price').addEventListener('input', updateMonthlyRate);
    document.getElementById('duration_months').addEventListener('input', updateMonthlyRate);

    // Initial calculation
    updateMonthlyRate();
    </script>
    <?php endif; ?>
</div>

<script>
function deletePlan(planId, planName) {
    showConfirmModal({
        title: 'Delete Plan',
        message: `Are you sure you want to delete plan <strong>${planName}</strong>?<br><br>This action cannot be undone.`,
        confirmText: 'Delete Plan',
        cancelText: 'Cancel',
        type: 'danger',
        onConfirm: () => {
            // Create and submit form
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            form.innerHTML = `
                <input type="hidden" name="plan_id" value="${planId}">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="csrf_token" value="<?= Auth::generateCsrfToken() ?>">
            `;

            document.body.appendChild(form);
            form.submit();
        }
    });
}
</script>

<?php include '../includes/footer.php'; ?>
