            </main>
        </div>
    </div>
    
    <!-- Mobile sidebar overlay -->
    <div x-show="sidebarOpen" 
         @click="sidebarOpen = false"
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"></div>
    
    <!-- Toast Container -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Enhanced Styles -->
    <style>
        @keyframes shrink {
            from { width: 100%; }
            to { width: 0%; }
        }

        .toast {
            backdrop-filter: blur(10px);
        }

        /* Enhanced modal animations */
        .modal-enter {
            animation: modalEnter 0.3s ease-out;
        }

        @keyframes modalEnter {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-10px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* Search input enhancements */
        .search-input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Button hover effects */
        .btn-hover {
            transition: all 0.2s ease;
        }

        .btn-hover:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Loading spinner */
        .spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Enhanced flash messages */
        .flash-message {
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>

    <!-- Global JavaScript -->
    <script>
        // Enhanced Toast notification system
        function showToast(message, type = 'info', duration = 5000) {
            // Create container if it doesn't exist
            let container = document.getElementById('toast-container');
            if (!container) {
                container = document.createElement('div');
                container.id = 'toast-container';
                container.className = 'fixed top-4 right-4 z-50 space-y-2';
                document.body.appendChild(container);
            }

            const toast = document.createElement('div');

            const icons = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };

            const colors = {
                success: 'bg-gradient-to-r from-green-50 to-green-100 border-green-200 text-green-800 dark:from-green-900 dark:to-green-800 dark:border-green-700 dark:text-green-200',
                error: 'bg-gradient-to-r from-red-50 to-red-100 border-red-200 text-red-800 dark:from-red-900 dark:to-red-800 dark:border-red-700 dark:text-red-200',
                warning: 'bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200 text-yellow-800 dark:from-yellow-900 dark:to-yellow-800 dark:border-yellow-700 dark:text-yellow-200',
                info: 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200 text-blue-800 dark:from-blue-900 dark:to-blue-800 dark:border-blue-700 dark:text-blue-200'
            };

            const iconColors = {
                success: 'text-green-500',
                error: 'text-red-500',
                warning: 'text-yellow-500',
                info: 'text-blue-500'
            };

            toast.className = `${colors[type]} border rounded-2xl p-4 shadow-xl toast max-w-sm transform transition-all duration-300 ease-out translate-x-full opacity-0`;
            toast.innerHTML = `
                <div class="flex items-start">
                    <div class="w-8 h-8 rounded-full bg-white dark:bg-gray-700 flex items-center justify-center mr-3 shadow-sm">
                        <i class="${icons[type]} ${iconColors[type]} text-sm"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium leading-5">${message}</p>
                    </div>
                    <button onclick="removeToast(this.closest('.toast'))" class="ml-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200 flex-shrink-0">
                        <i class="fas fa-times text-xs"></i>
                    </button>
                </div>
                <div class="absolute bottom-0 left-0 h-1 bg-current opacity-20 rounded-full transition-all duration-${duration}" style="width: 100%; animation: shrink ${duration}ms linear;"></div>
            `;

            container.appendChild(toast);

            // Animate in
            setTimeout(() => {
                toast.classList.remove('translate-x-full', 'opacity-0');
                toast.classList.add('translate-x-0', 'opacity-100');
            }, 10);

            // Auto remove after duration
            setTimeout(() => {
                removeToast(toast);
            }, duration);
        }

        function removeToast(toast) {
            if (toast && toast.parentElement) {
                toast.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.remove();
                    }
                }, 300);
            }
        }

        // Enhanced confirmation modal system
        function showConfirmModal(options = {}) {
            const {
                title = 'Confirm Action',
                message = 'Are you sure you want to proceed?',
                confirmText = 'Confirm',
                cancelText = 'Cancel',
                type = 'warning', // warning, danger, info
                onConfirm = () => {},
                onCancel = () => {}
            } = options;

            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';

            const typeColors = {
                warning: 'bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-400',
                danger: 'bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400',
                info: 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
            };

            const typeIcons = {
                warning: 'fas fa-exclamation-triangle',
                danger: 'fas fa-exclamation-circle',
                info: 'fas fa-info-circle'
            };

            const confirmColors = {
                warning: 'bg-yellow-600 hover:bg-yellow-700',
                danger: 'bg-red-600 hover:bg-red-700',
                info: 'bg-blue-600 hover:bg-blue-700'
            };

            modal.innerHTML = `
                <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-md w-full mx-4 shadow-xl modal-enter">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 ${typeColors[type]} rounded-full flex items-center justify-center mr-4">
                            <i class="${typeIcons[type]} text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">${title}</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">This action requires confirmation</p>
                        </div>
                    </div>
                    <div class="mb-6">
                        <p class="text-gray-700 dark:text-gray-300">${message}</p>
                    </div>
                    <div class="flex space-x-3">
                        <button class="cancel-btn flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition duration-200">
                            ${cancelText}
                        </button>
                        <button class="confirm-btn flex-1 px-4 py-2 ${confirmColors[type]} text-white rounded-lg transition duration-200">
                            ${confirmText}
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Event listeners
            const cancelBtn = modal.querySelector('.cancel-btn');
            const confirmBtn = modal.querySelector('.confirm-btn');

            cancelBtn.addEventListener('click', () => {
                modal.remove();
                onCancel();
            });

            confirmBtn.addEventListener('click', () => {
                modal.remove();
                onConfirm();
            });

            // Close on outside click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                    onCancel();
                }
            });

            // Close on escape key
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    modal.remove();
                    onCancel();
                    document.removeEventListener('keydown', handleEscape);
                }
            };
            document.addEventListener('keydown', handleEscape);

            return modal;
        }
        
        // Global search functionality
        let searchTimeout;
        function performSearch(query) {
            if (query.length < 3) return;
            
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                fetch(`api/search.php?q=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(data => {
                        // Handle search results
                        console.log('Search results:', data);
                    })
                    .catch(error => {
                        console.error('Search error:', error);
                    });
            }, 300);
        }
        
        // Auto-hide flash messages
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.toast');
            flashMessages.forEach(message => {
                setTimeout(() => {
                    if (message.parentElement) {
                        message.style.opacity = '0';
                        message.style.transform = 'translateX(100%)';
                        setTimeout(() => message.remove(), 300);
                    }
                }, 5000);
            });
        });
        
        // Enhanced confirm delete actions
        function confirmDelete(message = 'Are you sure you want to delete this item?', callback = null) {
            return new Promise((resolve) => {
                showConfirmModal({
                    title: 'Delete Confirmation',
                    message: message,
                    confirmText: 'Delete',
                    cancelText: 'Cancel',
                    type: 'danger',
                    onConfirm: () => {
                        if (callback) callback();
                        resolve(true);
                    },
                    onCancel: () => {
                        resolve(false);
                    }
                });
            });
        }
        
        // Format currency
        function formatCurrency(amount, currency = 'USD') {
            const symbols = {
                'USD': '$',
                'EUR': '€',
                'GBP': '£',
                'CAD': 'C$',
                'AUD': 'A$',
                'INR': '₹',
                'JPY': '¥'
            };
            
            const symbol = symbols[currency] || currency;
            return symbol + parseFloat(amount).toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }
        
        // Format date
        function formatDate(dateString, format = 'short') {
            const date = new Date(dateString);
            const options = {
                short: { year: 'numeric', month: 'short', day: 'numeric' },
                long: { year: 'numeric', month: 'long', day: 'numeric' },
                time: { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' }
            };
            
            return date.toLocaleDateString('en-US', options[format] || options.short);
        }
        
        // Loading state helper
        function setLoading(element, loading = true) {
            if (loading) {
                element.disabled = true;
                element.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';
            } else {
                element.disabled = false;
                // Restore original text (you might want to store this)
            }
        }
        
        // AJAX helper
        function ajaxRequest(url, options = {}) {
            const defaults = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            };
            
            return fetch(url, { ...defaults, ...options })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                });
        }
        
        // Form validation helper
        function validateForm(form) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('border-red-500');
                    isValid = false;
                } else {
                    field.classList.remove('border-red-500');
                }
            });
            
            return isValid;
        }
        
        // Auto-save functionality
        function autoSave(formId, endpoint) {
            const form = document.getElementById(formId);
            if (!form) return;
            
            let saveTimeout;
            const inputs = form.querySelectorAll('input, textarea, select');
            
            inputs.forEach(input => {
                input.addEventListener('input', () => {
                    clearTimeout(saveTimeout);
                    saveTimeout = setTimeout(() => {
                        const formData = new FormData(form);
                        
                        fetch(endpoint, {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                showToast('Changes saved automatically', 'success', 2000);
                            }
                        })
                        .catch(error => {
                            console.error('Auto-save error:', error);
                        });
                    }, 2000);
                });
            });
        }
        
        // Dark mode persistence
        document.addEventListener('alpine:init', () => {
            Alpine.store('darkMode', {
                on: localStorage.getItem('darkMode') === 'true',
                
                toggle() {
                    this.on = !this.on;
                    localStorage.setItem('darkMode', this.on);
                    document.documentElement.classList.toggle('dark', this.on);
                }
            });
            
            // Apply saved dark mode preference
            if (localStorage.getItem('darkMode') === 'true') {
                document.documentElement.classList.add('dark');
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('input[placeholder*="Search"]');
                if (searchInput) {
                    searchInput.focus();
                }
            }
            
            // Escape to close modals/dropdowns
            if (e.key === 'Escape') {
                // Close any open Alpine.js dropdowns
                document.dispatchEvent(new CustomEvent('click'));
            }
        });
        
        // Print functionality
        function printReceipt(receiptId) {
            const printWindow = window.open(`api/print-receipt.php?id=${receiptId}`, '_blank', 'width=400,height=600');
            printWindow.onload = function() {
                printWindow.print();
            };
        }

        // View receipt functionality
        function viewReceipt(receiptId) {
            const viewWindow = window.open(`api/view-receipt.php?id=${receiptId}`, '_blank', 'width=500,height=700');
        }
        
        // Export functionality
        function exportData(type, format = 'csv') {
            const url = `api/export.php?type=${type}&format=${format}`;
            const link = document.createElement('a');
            link.href = url;
            link.download = `${type}_export_${new Date().toISOString().split('T')[0]}.${format}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        // Image preview
        function previewImage(input, previewId) {
            const file = input.files[0];
            const preview = document.getElementById(previewId);
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }
        }
        
        // Initialize tooltips (if using a tooltip library)
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize any tooltips here
        });
    </script>
    
    <!-- Page-specific scripts can be added here -->
    <?php if (isset($additionalScripts)): ?>
        <?= $additionalScripts ?>
    <?php endif; ?>
</body>
</html>
