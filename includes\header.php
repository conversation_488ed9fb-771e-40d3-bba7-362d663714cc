<?php
if (!isset($_SESSION)) {
    session_start();
}

// Ensure Config class is available
if (!class_exists('Config')) {
    require_once __DIR__ . '/Config.php';
}

// Get gym name and settings
$gymName = Config::get('gym_name', 'MyGym');
$currentPage = basename($_SERVER['PHP_SELF'], '.php');

// Determine if we're in a subdirectory (like pages/)
$currentDir = dirname($_SERVER['PHP_SELF']);
$isInPages = (basename($currentDir) === 'pages');

// Set base path for navigation links
$basePath = $isInPages ? '../' : '';
$pagesPath = $isInPages ? '' : 'pages/';
?>
<!DOCTYPE html>
<html lang="en" x-data="{ darkMode: false, sidebarOpen: false }" :class="{ 'dark': darkMode }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? htmlspecialchars($pageTitle) . ' - ' : '' ?><?= htmlspecialchars($gymName) ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Custom Styles -->
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .toast { animation: slideInRight 0.3s ease-out; }
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg sidebar-transition transform lg:translate-x-0"
             :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full'">
            
            <!-- Sidebar Header -->
            <div class="flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-dumbbell text-white text-sm"></i>
                    </div>
                    <span class="text-xl font-bold text-gray-900 dark:text-white"><?= htmlspecialchars($gymName) ?></span>
                </div>
                <button @click="sidebarOpen = false" class="lg:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- Navigation -->
            <nav class="mt-6 px-3">
                <div class="space-y-1">
                    <!-- Dashboard -->
                    <a href="<?= $basePath ?>dashboard.php"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?= $currentPage === 'dashboard' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700' ?>">
                        <i class="fas fa-home mr-3"></i>
                        Dashboard
                    </a>

                    <!-- Members -->
                    <a href="<?= $pagesPath ?>members.php"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?= $currentPage === 'members' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700' ?>">
                        <i class="fas fa-users mr-3"></i>
                        Members
                    </a>

                    <!-- Trainers -->
                    <a href="<?= $pagesPath ?>trainers.php"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?= $currentPage === 'trainers' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700' ?>">
                        <i class="fas fa-user-tie mr-3"></i>
                        Trainers
                    </a>

                    <!-- Payments -->
                    <a href="<?= $pagesPath ?>payments.php"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?= $currentPage === 'payments' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700' ?>">
                        <i class="fas fa-credit-card mr-3"></i>
                        Payments
                    </a>

                    <!-- Renewals -->
                    <a href="<?= $pagesPath ?>renewals.php"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?= $currentPage === 'renewals' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700' ?>">
                        <i class="fas fa-sync-alt mr-3"></i>
                        Renewals
                    </a>

                    <!-- Plans -->
                    <a href="<?= $pagesPath ?>plans.php"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?= $currentPage === 'plans' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700' ?>">
                        <i class="fas fa-tags mr-3"></i>
                        Plans
                    </a>

                    <!-- Equipment -->
                    <a href="<?= $pagesPath ?>equipment.php"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?= $currentPage === 'equipment' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700' ?>">
                        <i class="fas fa-dumbbell mr-3"></i>
                        Equipment
                    </a>

                    <!-- Notifications -->
                    <a href="<?= $pagesPath ?>notifications.php"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?= $currentPage === 'notifications' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700' ?>">
                        <i class="fas fa-bell mr-3"></i>
                        Notifications
                        <?php
                        // Show notification count
                        try {
                            $notificationCount = Database::getInstance()->fetch("SELECT COUNT(*) as count FROM members WHERE status = 'active' AND end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)")['count'];
                            if ($notificationCount > 0): ?>
                                <span class="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1"><?= $notificationCount ?></span>
                            <?php endif;
                        } catch (Exception $e) {
                            // Ignore errors
                        }
                        ?>
                    </a>
                    
                    <!-- Reports -->
                    <a href="<?= $pagesPath ?>reports.php"
                       class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?= $currentPage === 'reports' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700' ?>">
                        <i class="fas fa-chart-bar mr-3"></i>
                        Reports
                    </a>
                </div>
                
                <!-- Admin Section -->
                <?php if (Auth::isAdmin()): ?>
                <div class="mt-8">
                    <div class="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider dark:text-gray-400">
                        Administration
                    </div>
                    <div class="mt-2 space-y-1">
                        <a href="<?= $pagesPath ?>users.php"
                           class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?= $currentPage === 'users' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700' ?>">
                            <i class="fas fa-user-shield mr-3"></i>
                            Users
                        </a>

                        <a href="<?= $pagesPath ?>settings.php"
                           class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?= $currentPage === 'settings' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700' ?>">
                            <i class="fas fa-cog mr-3"></i>
                            Settings
                        </a>

                        <a href="<?= $pagesPath ?>activity-logs.php"
                           class="flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200 <?= $currentPage === 'activity-logs' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700' ?>">
                            <i class="fas fa-history mr-3"></i>
                            Activity Logs
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden lg:ml-64">
            <!-- Top Navigation -->
            <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between h-16 px-6">
                    <!-- Mobile menu button -->
                    <button @click="sidebarOpen = true" class="lg:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    
                    <!-- Search -->
                    <div class="flex-1 max-w-lg mx-4">
                        <div class="relative" x-data="{ searchOpen: false, searchQuery: '' }">
                            <input type="text" 
                                   x-model="searchQuery"
                                   @focus="searchOpen = true"
                                   @blur="setTimeout(() => searchOpen = false, 200)"
                                   placeholder="Search members, receipts, payments..."
                                   class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            
                            <!-- Search Results Dropdown -->
                            <div x-show="searchOpen && searchQuery.length > 2" 
                                 x-transition
                                 class="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
                                <div class="p-2 text-sm text-gray-500 dark:text-gray-400">
                                    Search results will appear here...
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Right side -->
                    <div class="flex items-center space-x-4">
                        <!-- Dark mode toggle -->
                        <button @click="darkMode = !darkMode" 
                                class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="fas fa-moon" x-show="!darkMode"></i>
                            <i class="fas fa-sun" x-show="darkMode"></i>
                        </button>
                        
                        <!-- Notifications -->
                        <div class="relative" x-data="{ notificationOpen: false }">
                            <button @click="notificationOpen = !notificationOpen" 
                                    class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 relative">
                                <i class="fas fa-bell"></i>
                                <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                            </button>
                            
                            <!-- Notifications dropdown -->
                            <div x-show="notificationOpen" 
                                 @click.away="notificationOpen = false"
                                 x-transition
                                 class="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
                                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                                    <h3 class="text-sm font-semibold text-gray-900 dark:text-white">Notifications</h3>
                                </div>
                                <div class="max-h-64 overflow-y-auto">
                                    <div class="p-4 text-sm text-gray-500 dark:text-gray-400">
                                        No new notifications
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- User menu -->
                        <div class="relative" x-data="{ userMenuOpen: false }">
                            <button @click="userMenuOpen = !userMenuOpen" 
                                    class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm font-medium">
                                        <?= strtoupper(substr(Auth::user()['name'], 0, 1)) ?>
                                    </span>
                                </div>
                                <div class="hidden md:block text-left">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars(Auth::user()['name']) ?></p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 capitalize"><?= htmlspecialchars(Auth::user()['role']) ?></p>
                                </div>
                                <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                            </button>
                            
                            <!-- User dropdown -->
                            <div x-show="userMenuOpen" 
                                 @click.away="userMenuOpen = false"
                                 x-transition
                                 class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
                                <div class="py-1">
                                    <a href="<?= $pagesPath ?>profile.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                        <i class="fas fa-user mr-2"></i>Profile
                                    </a>
                                    <a href="<?= $pagesPath ?>settings.php" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                        <i class="fas fa-cog mr-2"></i>Settings
                                    </a>
                                    <div class="border-t border-gray-200 dark:border-gray-700"></div>
                                    <a href="<?= $basePath ?>logout.php" class="block px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700">
                                        <i class="fas fa-sign-out-alt mr-2"></i>Logout
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Enhanced Flash Messages -->
                <?php if (Session::hasFlash('success')): ?>
                    <div class="mb-6 bg-gradient-to-r from-green-50 to-green-100 border border-green-200 rounded-2xl p-4 shadow-lg flash-message dark:from-green-900 dark:to-green-800 dark:border-green-700">
                        <div class="flex items-start">
                            <div class="w-8 h-8 rounded-full bg-white dark:bg-gray-700 flex items-center justify-center mr-3 shadow-sm">
                                <i class="fas fa-check-circle text-green-500 text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-green-800 dark:text-green-200"><?= Session::getFlash('success') ?></p>
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-green-400 hover:text-green-600 dark:hover:text-green-300">
                                <i class="fas fa-times text-xs"></i>
                            </button>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (Session::hasFlash('error')): ?>
                    <div class="mb-6 bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-2xl p-4 shadow-lg flash-message dark:from-red-900 dark:to-red-800 dark:border-red-700">
                        <div class="flex items-start">
                            <div class="w-8 h-8 rounded-full bg-white dark:bg-gray-700 flex items-center justify-center mr-3 shadow-sm">
                                <i class="fas fa-exclamation-circle text-red-500 text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-red-800 dark:text-red-200"><?= htmlspecialchars(Session::getFlash('error')) ?></p>
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-red-400 hover:text-red-600 dark:hover:text-red-300">
                                <i class="fas fa-times text-xs"></i>
                            </button>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (Session::hasFlash('warning')): ?>
                    <div class="mb-6 bg-gradient-to-r from-yellow-50 to-yellow-100 border border-yellow-200 rounded-2xl p-4 shadow-lg flash-message dark:from-yellow-900 dark:to-yellow-800 dark:border-yellow-700">
                        <div class="flex items-start">
                            <div class="w-8 h-8 rounded-full bg-white dark:bg-gray-700 flex items-center justify-center mr-3 shadow-sm">
                                <i class="fas fa-exclamation-triangle text-yellow-500 text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-yellow-800 dark:text-yellow-200"><?= htmlspecialchars(Session::getFlash('warning')) ?></p>
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-yellow-400 hover:text-yellow-600 dark:hover:text-yellow-300">
                                <i class="fas fa-times text-xs"></i>
                            </button>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (Session::hasFlash('info')): ?>
                    <div class="mb-6 bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200 rounded-2xl p-4 shadow-lg flash-message dark:from-blue-900 dark:to-blue-800 dark:border-blue-700">
                        <div class="flex items-start">
                            <div class="w-8 h-8 rounded-full bg-white dark:bg-gray-700 flex items-center justify-center mr-3 shadow-sm">
                                <i class="fas fa-info-circle text-blue-500 text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-blue-800 dark:text-blue-200"><?= htmlspecialchars(Session::getFlash('info')) ?></p>
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-blue-400 hover:text-blue-600 dark:hover:text-blue-300">
                                <i class="fas fa-times text-xs"></i>
                            </button>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Check for access denied -->
                <?php if (isset($_GET['error']) && $_GET['error'] === 'access_denied'): ?>
                    <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                        <div class="flex">
                            <i class="fas fa-ban text-red-400 mt-0.5 mr-3"></i>
                            <span class="text-red-800">Access denied. You don't have permission to access this resource.</span>
                        </div>
                    </div>
                <?php endif; ?>
