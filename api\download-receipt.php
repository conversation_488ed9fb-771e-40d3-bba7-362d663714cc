<?php
/**
 * Download Receipt as PDF
 * MyGym Management System
 */

session_start();

// Check authentication (simple check)
if (!isset($_SESSION['user_id']) && !isset($_SESSION['local_admin'])) {
    http_response_code(401);
    exit('Unauthorized');
}

// Get payment ID
$paymentId = $_GET['id'] ?? null;
if (!$paymentId || !is_numeric($paymentId)) {
    http_response_code(400);
    exit('Invalid payment ID');
}

try {
    // Include database config
    $config = require_once '../config/config.php';

    // Create PDO connection
    $pdo = new PDO(
        "mysql:host={$config['database']['host']};dbname={$config['database']['name']};charset=utf8mb4",
        $config['database']['username'],
        $config['database']['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Get payment
    $stmt = $pdo->prepare("SELECT * FROM payments WHERE id = ?");
    $stmt->execute([$paymentId]);
    $payment = $stmt->fetch();

    if (!$payment) {
        http_response_code(404);
        exit('Payment not found');
    }

    // Get member
    $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
    $stmt->execute([$payment['member_id']]);
    $member = $stmt->fetch();

    if (!$member) {
        http_response_code(404);
        exit('Member not found');
    }

    // Get plan
    $stmt = $pdo->prepare("SELECT * FROM plans WHERE id = ?");
    $stmt->execute([$payment['plan_id']]);
    $plan = $stmt->fetch();

    // Get user (if exists)
    $processedByName = 'Local Admin';
    if ($payment['processed_by']) {
        $stmt = $pdo->prepare("SELECT name FROM users WHERE id = ?");
        $stmt->execute([$payment['processed_by']]);
        $user = $stmt->fetch();
        if ($user) {
            $processedByName = $user['name'];
        }
    }

    // Get gym settings
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM config WHERE setting_key IN ('gym_name', 'gym_address', 'gym_phone', 'gym_email', 'currency')");
    $stmt->execute();
    $settings = $stmt->fetchAll();

    $gymSettings = [];
    foreach ($settings as $setting) {
        $gymSettings[$setting['setting_key']] = $setting['setting_value'];
    }

    $gymName = $gymSettings['gym_name'] ?? 'MyGym';
    $gymAddress = $gymSettings['gym_address'] ?? '';
    $gymPhone = $gymSettings['gym_phone'] ?? '';
    $gymEmail = $gymSettings['gym_email'] ?? '';
    $currency = $gymSettings['currency'] ?? 'USD';
    
    // Set headers for PDF download
    $filename = 'Receipt_' . $payment['receipt_number'] . '.pdf';
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: private, max-age=0, must-revalidate');
    header('Pragma: public');
    
    // For now, we'll generate an HTML version that can be saved as PDF
    // In a production environment, you might want to use a library like TCPDF or mPDF
    
    // Generate HTML content for PDF
    $htmlContent = generateReceiptHTML($payment, $member, $plan, $processedByName, $gymName, $gymAddress, $gymPhone, $gymEmail, $currency);
    
    // Simple PDF generation using HTML to PDF conversion
    // This is a basic implementation - for production use a proper PDF library
    echo $htmlContent;
    
} catch (Exception $e) {
    http_response_code(500);
    exit('Error generating receipt PDF');
}

function generateReceiptHTML($payment, $member, $plan, $processedByName, $gymName, $gymAddress, $gymPhone, $gymEmail, $currency) {
    // Format currency function
    $formatCurrency = function($amount, $currency = 'USD') {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'INR' => '₹',
            'JPY' => '¥'
        ];

        $symbol = $symbols[$currency] ?? $currency . ' ';
        return $symbol . number_format($amount, 2);
    };
    
    return '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Receipt - ' . htmlspecialchars($payment['receipt_number']) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .gym-name {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .gym-info {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .receipt-title {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 30px 0 20px 0;
            color: #333;
        }
        
        .receipt-number {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 25px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }
        
        .label {
            font-weight: bold;
            color: #555;
        }
        
        .value {
            color: #333;
        }
        
        .total-section {
            background-color: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 30px 0;
        }
        
        .total-amount {
            font-size: 32px;
            font-weight: bold;
            color: #28a745;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #ddd;
            color: #666;
        }
        
        .notes {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        @media print {
            body { margin: 0; padding: 15px; }
            .section { break-inside: avoid; }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="gym-name">' . htmlspecialchars($gymName) . '</div>
        ' . ($gymAddress ? '<div class="gym-info">' . htmlspecialchars($gymAddress) . '</div>' : '') . '
        ' . ($gymPhone ? '<div class="gym-info">Tel: ' . htmlspecialchars($gymPhone) . '</div>' : '') . '
        ' . ($gymEmail ? '<div class="gym-info">' . htmlspecialchars($gymEmail) . '</div>' : '') . '
    </div>
    
    <!-- Receipt Title -->
    <div class="receipt-title">PAYMENT RECEIPT</div>
    
    <!-- Receipt Number -->
    <div class="receipt-number">Receipt #' . htmlspecialchars($payment['receipt_number']) . '</div>
    
    <!-- Member and Membership Information -->
    <div class="info-grid">
        <div class="section">
            <div class="section-title">👤 Member Information</div>
            <div class="info-row">
                <span class="label">Name:</span>
                <span class="value">' . htmlspecialchars($member['first_name'] . ' ' . $member['last_name']) . '</span>
            </div>
            <div class="info-row">
                <span class="label">Member ID:</span>
                <span class="value">' . htmlspecialchars($member['member_id']) . '</span>
            </div>
            ' . ($member['phone'] ? '
            <div class="info-row">
                <span class="label">Phone:</span>
                <span class="value">' . htmlspecialchars($member['phone']) . '</span>
            </div>' : '') . '
            ' . ($member['email'] ? '
            <div class="info-row">
                <span class="label">Email:</span>
                <span class="value">' . htmlspecialchars($member['email']) . '</span>
            </div>' : '') . '
        </div>
        
        <div class="section">
            <div class="section-title">🏋️ Membership Details</div>
            <div class="info-row">
                <span class="label">Plan:</span>
                <span class="value">' . htmlspecialchars($plan ? $plan['name'] : 'Unknown Plan') . '</span>
            </div>
            <div class="info-row">
                <span class="label">Duration:</span>
                <span class="value">' . ($plan ? $plan['duration_months'] : 0) . ' month(s)</span>
            </div>
            <div class="info-row">
                <span class="label">Start Date:</span>
                <span class="value">' . date('F j, Y', strtotime($payment['start_date'])) . '</span>
            </div>
            <div class="info-row">
                <span class="label">End Date:</span>
                <span class="value">' . date('F j, Y', strtotime($payment['end_date'])) . '</span>
            </div>
        </div>
    </div>
    
    <!-- Payment Information -->
    <div class="section">
        <div class="section-title">💳 Payment Information</div>
        <div class="info-grid">
            <div>
                <div class="info-row">
                    <span class="label">Payment Date:</span>
                    <span class="value">' . date('F j, Y', strtotime($payment['payment_date'])) . '</span>
                </div>
                <div class="info-row">
                    <span class="label">Payment Method:</span>
                    <span class="value">' . ucfirst(str_replace('_', ' ', $payment['payment_method'])) . '</span>
                </div>
            </div>
            <div>
                ' . ($payment['transaction_id'] ? '
                <div class="info-row">
                    <span class="label">Transaction ID:</span>
                    <span class="value">' . htmlspecialchars($payment['transaction_id']) . '</span>
                </div>' : '') . '
                <div class="info-row">
                    <span class="label">Processed By:</span>
                    <span class="value">' . htmlspecialchars($processedByName) . '</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Total Amount -->
    <div class="total-section">
        <div style="font-size: 18px; margin-bottom: 10px;">Total Amount Paid</div>
        <div class="total-amount">' . $formatCurrency($payment['amount'], $currency) . '</div>
    </div>
    
    ' . ($payment['notes'] && !strpos($payment['notes'], 'Local Admin') ? '
    <div class="notes">
        <strong>Notes:</strong><br>
        ' . htmlspecialchars(str_replace(' (Local Admin)', '', $payment['notes'])) . '
    </div>' : '') . '
    
    <!-- Footer -->
    <div class="footer">
        <p><strong>Thank you for your membership!</strong></p>
        <p>Generated on ' . date('F j, Y \a\t g:i A') . '</p>
    </div>
</body>
</html>';
}
?>
