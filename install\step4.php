<!-- Step 4: Database Installation -->
<div class="text-center mb-6">
    <i class="fas fa-rocket text-4xl text-blue-600 mb-4"></i>
    <h2 class="text-2xl font-bold text-gray-900 mb-2">Final Installation</h2>
    <p class="text-gray-600">Create database tables and complete setup</p>
</div>

<?php if (empty($success)): ?>
    <div class="space-y-6">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-4">Installation Summary</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <strong class="text-blue-800">Database:</strong>
                    <p class="text-blue-700"><?= htmlspecialchars($_SESSION['install_db']['name']) ?> @ <?= htmlspecialchars($_SESSION['install_db']['host']) ?></p>
                </div>
                <div>
                    <strong class="text-blue-800">Gym Name:</strong>
                    <p class="text-blue-700"><?= htmlspecialchars($_SESSION['install_site']['gym_name']) ?></p>
                </div>
                <div>
                    <strong class="text-blue-800">Site URL:</strong>
                    <p class="text-blue-700"><?= htmlspecialchars($_SESSION['install_site']['site_url']) ?></p>
                </div>
                <div>
                    <strong class="text-blue-800">Admin Email:</strong>
                    <p class="text-blue-700"><?= htmlspecialchars($_SESSION['install_admin']['email']) ?></p>
                </div>
                <div>
                    <strong class="text-blue-800">Currency:</strong>
                    <p class="text-blue-700"><?= htmlspecialchars($_SESSION['install_site']['currency']) ?></p>
                </div>
                <div>
                    <strong class="text-blue-800">Language:</strong>
                    <p class="text-blue-700"><?= htmlspecialchars($_SESSION['install_site']['language']) ?></p>
                </div>
            </div>
        </div>
        
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">What will be installed:</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div class="space-y-2">
                    <div class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>Database tables and structure</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>Default membership plans</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>Email campaign templates</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>Administrator account</span>
                    </div>
                </div>
                <div class="space-y-2">
                    <div class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>System configuration</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>User roles and permissions</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>Activity logging system</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-check text-green-500 mr-2"></i>
                        <span>Equipment management</span>
                    </div>
                </div>
            </div>
        </div>
        
        <form method="POST" class="text-center">
            <button type="submit" 
                    class="bg-green-600 hover:bg-green-700 text-white font-medium py-4 px-12 rounded-lg transition duration-200 flex items-center mx-auto text-lg">
                <i class="fas fa-play mr-3"></i>
                Install MyGym System
            </button>
        </form>
    </div>
<?php else: ?>
    <!-- Installation Complete -->
    <div class="text-center space-y-6">
        <div class="bg-green-50 border border-green-200 rounded-lg p-8">
            <i class="fas fa-check-circle text-6xl text-green-500 mb-4"></i>
            <h3 class="text-2xl font-bold text-green-900 mb-2">Installation Complete!</h3>
            <p class="text-green-700">Your MyGym Management System has been successfully installed.</p>
        </div>
        
        <div class="bg-white border border-gray-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">What's Next?</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-left">
                <div class="space-y-3">
                    <div class="flex items-start">
                        <i class="fas fa-sign-in-alt text-blue-500 mr-3 mt-1"></i>
                        <div>
                            <strong>Login to your admin panel</strong>
                            <p class="text-gray-600">Use the email and password you created</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-cog text-blue-500 mr-3 mt-1"></i>
                        <div>
                            <strong>Configure SMTP settings</strong>
                            <p class="text-gray-600">Set up email notifications in Settings</p>
                        </div>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="flex items-start">
                        <i class="fas fa-users text-blue-500 mr-3 mt-1"></i>
                        <div>
                            <strong>Add your first members</strong>
                            <p class="text-gray-600">Start managing your gym members</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-dumbbell text-blue-500 mr-3 mt-1"></i>
                        <div>
                            <strong>Add equipment and trainers</strong>
                            <p class="text-gray-600">Complete your gym setup</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex">
                <i class="fas fa-shield-alt text-yellow-400 mt-1 mr-3"></i>
                <div class="text-yellow-800 text-left">
                    <h4 class="font-medium">Security Recommendation:</h4>
                    <p class="mt-1 text-sm">For security, please delete the <code class="bg-yellow-200 px-1 rounded">install.php</code> file and <code class="bg-yellow-200 px-1 rounded">install/</code> folder after installation.</p>
                </div>
            </div>
        </div>
        
        <div class="flex justify-center space-x-4">
            <a href="index.php" 
               class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-lg transition duration-200 flex items-center">
                <i class="fas fa-sign-in-alt mr-2"></i>
                Login to Admin Panel
            </a>
            
            <a href="<?= htmlspecialchars($_SESSION['install_site']['site_url'] ?? '#') ?>" 
               class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-8 rounded-lg transition duration-200 flex items-center">
                <i class="fas fa-external-link-alt mr-2"></i>
                Visit Site
            </a>
        </div>
    </div>
<?php endif; ?>
